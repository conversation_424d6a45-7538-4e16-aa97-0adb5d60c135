{"name": "@next-auth/prisma-adapter", "version": "1.0.7", "description": "Prisma adapter for next-auth.", "homepage": "https://authjs.dev", "repository": "https://github.com/nextauthjs/next-auth", "bugs": {"url": "https://github.com/nextauthjs/next-auth/issues"}, "author": "<PERSON>", "main": "dist/index.js", "license": "ISC", "keywords": ["next-auth", "next.js", "o<PERSON>h", "prisma"], "private": false, "publishConfig": {"access": "public"}, "files": ["README.md", "dist"], "peerDependencies": {"@prisma/client": ">=2.26.0 || >=3", "next-auth": "^4"}, "devDependencies": {"@prisma/client": "^3.10.0", "jest": "^27.4.3", "mongodb": "^4.4.0", "prisma": "^3.10.0", "@next-auth/adapter-test": "0.0.0", "next-auth": "4.22.1", "@next-auth/tsconfig": "0.0.0"}, "jest": {"preset": "@next-auth/adapter-test/jest"}, "scripts": {"clean": "rm ./prisma/dev.db* || echo 'File deleted'", "init:default": "prisma migrate dev --name init --skip-seed", "init:custom": "prisma migrate dev --name init-custom --schema ./prisma/custom.prisma", "test:default": "pnpm init:default && jest", "test:custom": "pnpm init:custom && CUSTOM_MODEL=1 jest", "test:mongodb": "./tests/mongodb.test.sh", "test": "pnpm test:default && pnpm test:custom && pnpm test:mongodb", "build": "prisma generate && tsc", "dev": "prisma generate && tsc -w", "studio": "prisma studio"}}