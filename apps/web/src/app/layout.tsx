import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import { Providers } from '@/components/providers';
import './globals.css';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'TrendPulse - AI-Powered Trend Analysis',
  description: 'Get daily trend analysis reports powered by AI. Track keywords, analyze sentiment, and stay ahead of the curve.',
  keywords: ['trend analysis', 'AI', 'reports', 'keywords', 'sentiment analysis'],
  authors: [{ name: 'TrendPulse Team' }],
  creator: 'TrendPulse',
  publisher: 'TrendPulse',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.APP_URL || 'http://localhost:3000'),
  openGraph: {
    title: 'TrendPulse - AI-Powered Trend Analysis',
    description: 'Get daily trend analysis reports powered by AI. Track keywords, analyze sentiment, and stay ahead of the curve.',
    url: '/',
    siteName: 'TrendPulse',
    images: [
      {
        url: '/og-image.png',
        width: 1200,
        height: 630,
        alt: 'TrendPulse - AI-Powered Trend Analysis',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'TrendPulse - AI-Powered Trend Analysis',
    description: 'Get daily trend analysis reports powered by AI. Track keywords, analyze sentiment, and stay ahead of the curve.',
    images: ['/og-image.png'],
    creator: '@trendpulse',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <Providers>
          {children}
        </Providers>
      </body>
    </html>
  );
}
