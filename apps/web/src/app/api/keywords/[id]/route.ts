import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { z } from 'zod';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/db';

const updateKeywordSchema = z.object({
  term: z.string().min(1).optional(),
  description: z.string().optional(),
  category: z.string().optional(),
  tags: z.array(z.string()).optional(),
  priority: z.number().min(1).max(5).optional(),
  isActive: z.boolean().optional(),
});

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const keyword = await prisma.keyword.findFirst({
      where: {
        id: params.id,
        userId: session.user.id,
      },
      include: {
        reports: {
          orderBy: { reportDate: 'desc' },
          take: 5,
        },
        trendData: {
          orderBy: { date: 'desc' },
          take: 30,
        },
        newsArticles: {
          orderBy: { publishedAt: 'desc' },
          take: 10,
        },
        socialPosts: {
          orderBy: { publishedAt: 'desc' },
          take: 10,
        },
        _count: {
          select: {
            reports: true,
            trendData: true,
            newsArticles: true,
            socialPosts: true,
          },
        },
      },
    });

    if (!keyword) {
      return NextResponse.json({ error: 'Keyword not found' }, { status: 404 });
    }

    return NextResponse.json(keyword);
  } catch (error) {
    console.error('Error fetching keyword:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const updateData = updateKeywordSchema.parse(body);

    // Check if keyword exists and belongs to user
    const existingKeyword = await prisma.keyword.findFirst({
      where: {
        id: params.id,
        userId: session.user.id,
      },
    });

    if (!existingKeyword) {
      return NextResponse.json({ error: 'Keyword not found' }, { status: 404 });
    }

    // If updating term, check for duplicates
    if (updateData.term && updateData.term !== existingKeyword.term) {
      const duplicate = await prisma.keyword.findFirst({
        where: {
          userId: session.user.id,
          term: updateData.term.toLowerCase(),
          id: { not: params.id },
        },
      });

      if (duplicate) {
        return NextResponse.json(
          { error: 'Keyword with this term already exists' },
          { status: 400 }
        );
      }
    }

    const keyword = await prisma.keyword.update({
      where: { id: params.id },
      data: {
        ...updateData,
        term: updateData.term?.toLowerCase(),
        updatedAt: new Date(),
      },
      include: {
        _count: {
          select: {
            reports: true,
            trendData: true,
            newsArticles: true,
            socialPosts: true,
          },
        },
      },
    });

    return NextResponse.json(keyword);
  } catch (error) {
    console.error('Error updating keyword:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if keyword exists and belongs to user
    const keyword = await prisma.keyword.findFirst({
      where: {
        id: params.id,
        userId: session.user.id,
      },
    });

    if (!keyword) {
      return NextResponse.json({ error: 'Keyword not found' }, { status: 404 });
    }

    // Delete keyword and all related data (cascade delete)
    await prisma.keyword.delete({
      where: { id: params.id },
    });

    return NextResponse.json({ message: 'Keyword deleted successfully' });
  } catch (error) {
    console.error('Error deleting keyword:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
