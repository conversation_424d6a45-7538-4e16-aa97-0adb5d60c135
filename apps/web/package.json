{"name": "@trendpulse/web", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "vitest", "test:e2e": "playwright test", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "type-check": "tsc --noEmit"}, "dependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@hookform/resolvers": "^3.3.2", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^5.8.0", "@react-email/components": "^0.0.12", "@tanstack/react-query": "^5.17.0", "@tanstack/react-query-devtools": "^5.81.5", "autoprefixer": "^10.4.16", "axios": "^1.6.5", "bcryptjs": "^2.4.3", "bullmq": "^4.15.0", "cheerio": "^1.0.0-rc.12", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "express-rate-limit": "^7.1.5", "framer-motion": "^10.18.0", "google-trends-api": "^4.9.2", "helmet": "^7.1.0", "ioredis": "^5.3.2", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.312.0", "next": "^14.1.0", "next-auth": "^4.24.5", "next-themes": "^0.4.6", "node-cron": "^3.0.3", "openai": "^4.24.1", "pino": "^8.17.2", "pino-pretty": "^10.3.1", "postcss": "^8.4.33", "react": "^18.2.0", "react-dom": "^18.2.0", "react-email": "^1.10.0", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.5.2", "react-wordcloud": "^1.2.7", "recharts": "^2.10.3", "resend": "^3.0.0", "rss-parser": "^3.13.0", "sharp": "^0.33.1", "tailwind-merge": "^2.2.0", "tailwindcss": "^3.4.0", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@playwright/test": "^1.40.1", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^20.11.0", "@types/node-cron": "^3.0.11", "@types/react": "^18.2.48", "@types/react-dom": "^18.2.18", "@typescript-eslint/eslint-plugin": "^6.18.1", "@typescript-eslint/parser": "^6.18.1", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.56.0", "eslint-config-next": "^14.1.0", "husky": "^8.0.3", "lint-staged": "^15.2.0", "playwright": "^1.40.1", "prettier": "^3.2.0", "prettier-plugin-tailwindcss": "^0.5.10", "prisma": "^5.8.0", "typescript": "^5.3.3", "vitest": "^1.2.0"}}