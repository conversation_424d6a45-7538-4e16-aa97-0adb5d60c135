import {
  Html,
  Head,
  Body,
  Container,
  Section,
  Text,
  Heading,
  Button,
  Hr,
  Img,
} from '@react-email/components';

interface DailyReportEmailProps {
  name: string;
  reports: Array<{
    keyword: { term: string };
    trendScore: number;
    sentimentScore: number;
    insights: {
      summary: string;
      keyPoints: string[];
      recommendations: string[];
    };
    newsArticles: Array<{ title: string; url: string }>;
  }>;
  date: string;
}

export function DailyReportEmail({ name, reports, date }: DailyReportEmailProps) {
  const baseUrl = process.env.APP_URL || 'http://localhost:3000';

  return (
    <Html>
      <Head />
      <Body style={main}>
        <Container style={container}>
          {/* Header */}
          <Section style={header}>
            <Heading style={headerTitle}>TrendPulse</Heading>
            <Text style={headerSubtitle}>Your Daily Trend Analysis Report</Text>
          </Section>

          {/* Greeting */}
          <Section style={content}>
            <Text style={greeting}>Good morning, {name}!</Text>
            <Text style={intro}>
              Here's your trend analysis report for {date}. We've analyzed {reports.length} keyword{reports.length > 1 ? 's' : ''} and compiled the latest insights for you.
            </Text>
          </Section>

          {/* Reports */}
          {reports.map((report, index) => (
            <Section key={index} style={reportSection}>
              <Heading style={keywordTitle}>{report.keyword.term}</Heading>
              
              {/* Metrics */}
              <Section style={metricsSection}>
                <div style={metricItem}>
                  <Text style={metricLabel}>Trend Score</Text>
                  <Text style={metricValue}>{Math.round(report.trendScore)}/100</Text>
                </div>
                <div style={metricItem}>
                  <Text style={metricLabel}>Sentiment</Text>
                  <Text style={{
                    ...metricValue,
                    color: report.sentimentScore > 0.1 ? '#10B981' : 
                           report.sentimentScore < -0.1 ? '#EF4444' : '#F59E0B'
                  }}>
                    {report.sentimentScore > 0.1 ? 'Positive' : 
                     report.sentimentScore < -0.1 ? 'Negative' : 'Neutral'}
                  </Text>
                </div>
              </Section>

              {/* Summary */}
              <Text style={summary}>{report.insights.summary}</Text>

              {/* Key Points */}
              <div style={section}>
                <Text style={sectionTitle}>Key Insights</Text>
                <ul style={list}>
                  {report.insights.keyPoints.map((point, i) => (
                    <li key={i} style={listItem}>{point}</li>
                  ))}
                </ul>
              </div>

              {/* Recommendations */}
              <div style={section}>
                <Text style={sectionTitle}>Recommendations</Text>
                <ul style={list}>
                  {report.insights.recommendations.map((rec, i) => (
                    <li key={i} style={listItem}>{rec}</li>
                  ))}
                </ul>
              </div>

              {/* Top News */}
              {report.newsArticles.length > 0 && (
                <div style={section}>
                  <Text style={sectionTitle}>Top News</Text>
                  {report.newsArticles.slice(0, 3).map((article, i) => (
                    <div key={i} style={newsItem}>
                      <Text style={newsTitle}>{article.title}</Text>
                    </div>
                  ))}
                </div>
              )}

              {index < reports.length - 1 && <Hr style={divider} />}
            </Section>
          ))}

          {/* CTA */}
          <Section style={ctaSection}>
            <Text style={ctaText}>
              Want to dive deeper into your trends? View the full interactive report in your dashboard.
            </Text>
            <Button style={ctaButton} href={`${baseUrl}/dashboard`}>
              View Full Report
            </Button>
          </Section>

          {/* Footer */}
          <Section style={footer}>
            <Text style={footerText}>
              You're receiving this email because you have daily reports enabled for your TrendPulse account.
            </Text>
            <Text style={footerText}>
              <a href={`${baseUrl}/settings`} style={footerLink}>Manage preferences</a> | 
              <a href={`${baseUrl}/unsubscribe`} style={footerLink}>Unsubscribe</a>
            </Text>
          </Section>
        </Container>
      </Body>
    </Html>
  );
}

// Styles
const main = {
  backgroundColor: '#f6f9fc',
  fontFamily: '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
};

const container = {
  backgroundColor: '#ffffff',
  margin: '0 auto',
  padding: '20px 0 48px',
  marginBottom: '64px',
  maxWidth: '600px',
};

const header = {
  background: 'linear-gradient(135deg, #4F46E5 0%, #22D3EE 100%)',
  padding: '30px',
  textAlign: 'center' as const,
  borderRadius: '10px 10px 0 0',
};

const headerTitle = {
  color: '#ffffff',
  fontSize: '28px',
  fontWeight: 'bold',
  margin: '0',
};

const headerSubtitle = {
  color: '#ffffff',
  fontSize: '16px',
  margin: '8px 0 0 0',
  opacity: 0.9,
};

const content = {
  padding: '30px',
};

const greeting = {
  fontSize: '18px',
  fontWeight: 'bold',
  color: '#1f2937',
  margin: '0 0 16px 0',
};

const intro = {
  fontSize: '16px',
  color: '#6b7280',
  lineHeight: '24px',
  margin: '0 0 24px 0',
};

const reportSection = {
  padding: '0 30px',
  marginBottom: '32px',
};

const keywordTitle = {
  fontSize: '20px',
  fontWeight: 'bold',
  color: '#1f2937',
  margin: '0 0 16px 0',
  textTransform: 'capitalize' as const,
};

const metricsSection = {
  display: 'flex',
  gap: '24px',
  marginBottom: '16px',
};

const metricItem = {
  flex: 1,
  textAlign: 'center' as const,
  padding: '16px',
  backgroundColor: '#f9fafb',
  borderRadius: '8px',
};

const metricLabel = {
  fontSize: '12px',
  color: '#6b7280',
  textTransform: 'uppercase' as const,
  fontWeight: 'bold',
  margin: '0 0 4px 0',
};

const metricValue = {
  fontSize: '18px',
  fontWeight: 'bold',
  color: '#1f2937',
  margin: '0',
};

const summary = {
  fontSize: '16px',
  color: '#374151',
  lineHeight: '24px',
  margin: '0 0 24px 0',
};

const section = {
  marginBottom: '24px',
};

const sectionTitle = {
  fontSize: '16px',
  fontWeight: 'bold',
  color: '#1f2937',
  margin: '0 0 12px 0',
};

const list = {
  margin: '0',
  paddingLeft: '20px',
};

const listItem = {
  fontSize: '14px',
  color: '#374151',
  lineHeight: '20px',
  marginBottom: '8px',
};

const newsItem = {
  marginBottom: '12px',
  paddingBottom: '12px',
  borderBottom: '1px solid #e5e7eb',
};

const newsTitle = {
  fontSize: '14px',
  color: '#1f2937',
  fontWeight: '500',
  margin: '0',
};

const divider = {
  borderColor: '#e5e7eb',
  margin: '32px 0',
};

const ctaSection = {
  padding: '30px',
  textAlign: 'center' as const,
  backgroundColor: '#f9fafb',
  borderRadius: '8px',
  margin: '0 30px',
};

const ctaText = {
  fontSize: '16px',
  color: '#374151',
  margin: '0 0 20px 0',
};

const ctaButton = {
  backgroundColor: '#4F46E5',
  borderRadius: '6px',
  color: '#ffffff',
  fontSize: '16px',
  fontWeight: 'bold',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'inline-block',
  padding: '12px 24px',
};

const footer = {
  padding: '30px',
  textAlign: 'center' as const,
  borderTop: '1px solid #e5e7eb',
  marginTop: '32px',
};

const footerText = {
  fontSize: '12px',
  color: '#6b7280',
  margin: '0 0 8px 0',
};

const footerLink = {
  color: '#4F46E5',
  textDecoration: 'none',
  margin: '0 8px',
};
