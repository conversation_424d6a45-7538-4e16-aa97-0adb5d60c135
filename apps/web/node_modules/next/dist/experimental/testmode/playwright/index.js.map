{"version": 3, "sources": ["../../../../src/experimental/testmode/playwright/index.ts"], "names": ["defineConfig", "test", "config", "base", "extend", "nextOptions", "fetch<PERSON><PERSON><PERSON>", "option", "_next<PERSON><PERSON><PERSON>", "use", "applyNextWorkerFixture", "scope", "auto", "next", "page", "testInfo", "applyNextFixture", "nextWorker"], "mappings": "AAAA,6DAA6D;;;;;;;;;;;;;;;;;IAmD7D,OAAmB;eAAnB;;IA9BgBA,YAAY;eAAZA;;IAMHC,IAAI;eAAJA;;;;2EA1BS;mCAIiB;6BACN;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAe1B,SAASD,aACdE,MAAoC;IAEpC,OAAOC,MAAKH,YAAY,CAAIE;AAC9B;AAEO,MAAMD,OAAOE,MAAKF,IAAI,CAACG,MAAM,CAGlC;IACAC,aAAa;QAAC;YAAEC,eAAe;QAAM;QAAG;YAAEC,QAAQ;QAAK;KAAE;IAEzDC,aAAa;QACX,4CAA4C;QAC5C,OAAO,EAAE,EAAEC;YACT,MAAMC,IAAAA,yCAAsB,EAACD;QAC/B;QACA;YAAEE,OAAO;YAAUC,MAAM;QAAK;KAC/B;IAEDC,MAAM,OAAO,EAAER,WAAW,EAAEG,WAAW,EAAEM,IAAI,EAAE,EAAEL,KAAKM;QACpD,MAAMC,IAAAA,6BAAgB,EAACP,KAAK;YAC1BM;YACAE,YAAYT;YACZM;YACAT;QACF;IACF;AACF;MAEA,WAAeJ"}