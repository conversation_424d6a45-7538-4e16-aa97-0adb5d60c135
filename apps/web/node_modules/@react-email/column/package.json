{"name": "@react-email/column", "version": "0.0.8", "description": "Display a column that separates content areas vertically in your email", "sideEffects": false, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist/**"], "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}}, "license": "MIT", "scripts": {"build": "tsup src/index.ts --format esm,cjs --dts --external react", "clean": "rm -rf dist", "dev": "tsup src/index.ts --format esm,cjs --dts --external react --watch", "lint": "eslint .", "test:watch": "vitest", "test": "vitest run"}, "repository": {"type": "git", "url": "https://github.com/resendlabs/react-email.git", "directory": "packages/column"}, "keywords": ["react", "email"], "engines": {"node": ">=18.0.0"}, "peerDependencies": {"react": "18.2.0"}, "devDependencies": {"@babel/preset-react": "7.22.5", "@react-email/render": "workspace:*", "eslint-config-custom": "workspace:*", "tsconfig": "workspace:*", "typescript": "5.1.6"}, "publishConfig": {"access": "public"}}