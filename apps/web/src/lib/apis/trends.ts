import googleTrends from 'google-trends-api';

export interface TrendData {
  date: string;
  value: number;
  formattedDate: string;
  formattedValue: string;
}

export interface RelatedQuery {
  query: string;
  value: number;
  formattedValue: string;
}

export async function fetchGoogleTrends(
  keyword: string,
  timeframe: string = 'today 7-d',
  geo: string = 'US'
): Promise<TrendData[]> {
  try {
    const results = await googleTrends.interestOverTime({
      keyword: keyword,
      startTime: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
      endTime: new Date(),
      geo: geo,
    });

    const data = JSON.parse(results);
    const timeline = data.default.timelineData;

    return timeline.map((item: any) => ({
      date: new Date(item.time * 1000).toISOString(),
      value: item.value[0] || 0,
      formattedDate: new Date(item.time * 1000).toLocaleDateString(),
      formattedValue: `${item.value[0] || 0}%`,
    }));
  } catch (error) {
    console.error('Error fetching Google Trends:', error);
    // Return mock data for development
    return generateMockTrendData();
  }
}

export async function fetchRelatedQueries(
  keyword: string,
  geo: string = 'US'
): Promise<RelatedQuery[]> {
  try {
    const results = await googleTrends.relatedQueries({
      keyword: keyword,
      startTime: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
      endTime: new Date(),
      geo: geo,
    });

    const data = JSON.parse(results);
    const related = data.default.rankedList[0]?.rankedKeyword || [];

    return related.slice(0, 10).map((item: any) => ({
      query: item.query,
      value: item.value,
      formattedValue: `${item.value}%`,
    }));
  } catch (error) {
    console.error('Error fetching related queries:', error);
    return [];
  }
}

export async function fetchDailySearchTrend(keyword: string): Promise<number> {
  try {
    const results = await googleTrends.dailyTrends({
      trendDate: new Date(),
      geo: 'US',
    });

    const data = JSON.parse(results);
    const trends = data.default.trendingSearchesDays[0]?.trendingSearches || [];
    
    // Find if our keyword is in trending searches
    const matchingTrend = trends.find((trend: any) =>
      trend.title.query.toLowerCase().includes(keyword.toLowerCase())
    );

    if (matchingTrend) {
      return parseInt(matchingTrend.formattedTraffic.replace(/[^\d]/g, '')) || 0;
    }

    // If not in trending, fetch regular interest
    const interestData = await fetchGoogleTrends(keyword, 'now 1-d');
    return interestData[interestData.length - 1]?.value || 0;
  } catch (error) {
    console.error('Error fetching daily search trend:', error);
    return 0;
  }
}

export async function compareKeywords(keywords: string[]): Promise<any> {
  try {
    const results = await googleTrends.interestOverTime({
      keyword: keywords,
      startTime: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      endTime: new Date(),
    });

    return JSON.parse(results);
  } catch (error) {
    console.error('Error comparing keywords:', error);
    return null;
  }
}

function generateMockTrendData(): TrendData[] {
  const data: TrendData[] = [];
  const now = new Date();
  
  for (let i = 6; i >= 0; i--) {
    const date = new Date(now);
    date.setDate(date.getDate() - i);
    
    const value = Math.floor(Math.random() * 100) + 1;
    
    data.push({
      date: date.toISOString(),
      value: value,
      formattedDate: date.toLocaleDateString(),
      formattedValue: `${value}%`,
    });
  }
  
  return data;
}

export function calculateTrendScore(trendData: TrendData[]): number {
  if (trendData.length === 0) return 0;
  
  const values = trendData.map(d => d.value);
  const average = values.reduce((sum, val) => sum + val, 0) / values.length;
  
  // Calculate trend direction (positive if increasing, negative if decreasing)
  const firstHalf = values.slice(0, Math.floor(values.length / 2));
  const secondHalf = values.slice(Math.floor(values.length / 2));
  
  const firstAvg = firstHalf.reduce((sum, val) => sum + val, 0) / firstHalf.length;
  const secondAvg = secondHalf.reduce((sum, val) => sum + val, 0) / secondHalf.length;
  
  const trendDirection = secondAvg - firstAvg;
  
  // Combine average value with trend direction
  return Math.min(100, Math.max(0, average + trendDirection));
}
