'use client';

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import { KeywordWithCounts, KeywordFormData, SearchFilters } from '@/types';

// Fetch keywords
export function useKeywords(filters?: SearchFilters) {
  return useQuery({
    queryKey: ['keywords', filters],
    queryFn: async () => {
      const params = new URLSearchParams();
      
      if (filters?.search) params.append('search', filters.search);
      if (filters?.category) params.append('category', filters.category);
      if (filters?.active !== undefined) params.append('active', filters.active.toString());
      
      const response = await fetch(`/api/keywords?${params}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch keywords');
      }
      
      return response.json();
    },
  });
}

// Fetch single keyword
export function useKeyword(id: string) {
  return useQuery({
    queryKey: ['keyword', id],
    queryFn: async () => {
      const response = await fetch(`/api/keywords/${id}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch keyword');
      }
      
      return response.json();
    },
    enabled: !!id,
  });
}

// Create keyword
export function useCreateKeyword() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (data: KeywordFormData) => {
      const response = await fetch('/api/keywords', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to create keyword');
      }
      
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['keywords'] });
      toast.success('Keyword created successfully!');
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
}

// Update keyword
export function useUpdateKeyword() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: Partial<KeywordFormData> }) => {
      const response = await fetch(`/api/keywords/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to update keyword');
      }
      
      return response.json();
    },
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['keywords'] });
      queryClient.invalidateQueries({ queryKey: ['keyword', id] });
      toast.success('Keyword updated successfully!');
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
}

// Delete keyword
export function useDeleteKeyword() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (id: string) => {
      const response = await fetch(`/api/keywords/${id}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to delete keyword');
      }
      
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['keywords'] });
      toast.success('Keyword deleted successfully!');
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
}

// Toggle keyword active status
export function useToggleKeyword() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ id, isActive }: { id: string; isActive: boolean }) => {
      const response = await fetch(`/api/keywords/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ isActive }),
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to update keyword');
      }
      
      return response.json();
    },
    onSuccess: (_, { isActive }) => {
      queryClient.invalidateQueries({ queryKey: ['keywords'] });
      toast.success(`Keyword ${isActive ? 'activated' : 'deactivated'} successfully!`);
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
}
