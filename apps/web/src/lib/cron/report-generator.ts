import cron from 'node-cron';
import { prisma } from '@/lib/db';
import { fetchNews } from '@/lib/apis/news';
import { fetchGoogleTrends, calculateTrendScore } from '@/lib/apis/trends';
import { fetchAllSocialPosts, calculateSocialSentiment } from '@/lib/apis/social';
import { generateTrendInsights, analyzeSentiment, summarizeNews } from '@/lib/apis/openai';
import { sendReportEmail } from '@/lib/email';
import { logger } from '@/lib/logger';

interface ReportData {
  keyword: any;
  trendData: any[];
  newsArticles: any[];
  socialPosts: any[];
  trendScore: number;
  sentimentScore: number;
  insights: any;
  newsSummary: string;
}

export class ReportGenerator {
  private isRunning = false;

  constructor() {
    this.initializeCronJobs();
  }

  private initializeCronJobs() {
    // Daily report generation at 10:00 AM for each user's timezone
    cron.schedule('0 10 * * *', async () => {
      await this.generateDailyReports();
    });

    // Hourly data collection
    cron.schedule('0 * * * *', async () => {
      await this.collectHourlyData();
    });

    // Weekly cleanup of old data
    cron.schedule('0 2 * * 0', async () => {
      await this.cleanupOldData();
    });

    logger.info('Cron jobs initialized');
  }

  async generateDailyReports() {
    if (this.isRunning) {
      logger.warn('Report generation already in progress, skipping...');
      return;
    }

    this.isRunning = true;
    logger.info('Starting daily report generation');

    try {
      // Get all active users with their keywords
      const users = await prisma.user.findMany({
        where: {
          isActive: true,
          emailEnabled: true,
        },
        include: {
          keywords: {
            where: { isActive: true },
          },
          preferences: true,
        },
      });

      for (const user of users) {
        try {
          await this.generateUserReports(user);
        } catch (error) {
          logger.error(`Error generating reports for user ${user.id}:`, error);
        }
      }

      logger.info('Daily report generation completed');
    } catch (error) {
      logger.error('Error in daily report generation:', error);
    } finally {
      this.isRunning = false;
    }
  }

  private async generateUserReports(user: any) {
    const { keywords, preferences } = user;
    
    if (keywords.length === 0) {
      logger.info(`No keywords found for user ${user.id}, skipping...`);
      return;
    }

    logger.info(`Generating reports for user ${user.id} with ${keywords.length} keywords`);

    const reports: ReportData[] = [];

    for (const keyword of keywords) {
      try {
        const reportData = await this.generateKeywordReport(keyword);
        reports.push(reportData);

        // Save report to database
        await this.saveReport(user.id, keyword.id, reportData);
      } catch (error) {
        logger.error(`Error generating report for keyword ${keyword.term}:`, error);
      }
    }

    // Send email with all reports
    if (reports.length > 0) {
      await this.sendUserReportEmail(user, reports);
    }
  }

  private async generateKeywordReport(keyword: any): Promise<ReportData> {
    logger.info(`Generating report for keyword: ${keyword.term}`);

    // Fetch data from various sources
    const [trendData, newsArticles, socialPosts] = await Promise.all([
      fetchGoogleTrends(keyword.term, 'today 7-d'),
      fetchNews(keyword.term, 1),
      fetchAllSocialPosts(keyword.term),
    ]);

    // Calculate scores
    const trendScore = calculateTrendScore(trendData);
    const sentimentScore = calculateSocialSentiment(socialPosts);

    // Generate AI insights
    const insights = await generateTrendInsights(
      keyword.term,
      newsArticles,
      socialPosts,
      trendData
    );

    // Generate news summary
    const newsSummary = await summarizeNews(newsArticles.slice(0, 5));

    // Store raw data in database
    await this.storeRawData(keyword.id, {
      trendData,
      newsArticles,
      socialPosts,
    });

    return {
      keyword,
      trendData,
      newsArticles,
      socialPosts,
      trendScore,
      sentimentScore,
      insights,
      newsSummary,
    };
  }

  private async storeRawData(keywordId: string, data: any) {
    const { trendData, newsArticles, socialPosts } = data;

    // Store trend data
    for (const trend of trendData) {
      await prisma.trendData.upsert({
        where: {
          keywordId_date_region_source: {
            keywordId,
            date: new Date(trend.date),
            region: 'US',
            source: 'google_trends',
          },
        },
        update: {
          trendScore: trend.value,
          rawData: trend,
        },
        create: {
          keywordId,
          date: new Date(trend.date),
          trendScore: trend.value,
          region: 'US',
          source: 'google_trends',
          rawData: trend,
        },
      });
    }

    // Store news articles
    for (const article of newsArticles) {
      await prisma.newsArticle.upsert({
        where: {
          keywordId_url: {
            keywordId,
            url: article.url,
          },
        },
        update: {
          title: article.title,
          description: article.description,
          content: article.content,
          source: article.source,
          author: article.author,
          publishedAt: new Date(article.publishedAt),
        },
        create: {
          keywordId,
          title: article.title,
          description: article.description,
          content: article.content,
          url: article.url,
          imageUrl: article.imageUrl,
          source: article.source,
          author: article.author,
          publishedAt: new Date(article.publishedAt),
        },
      });
    }

    // Store social posts
    for (const post of socialPosts) {
      await prisma.socialPost.upsert({
        where: {
          keywordId_platform_postId: {
            keywordId,
            platform: post.platform.toUpperCase() as any,
            postId: post.id,
          },
        },
        update: {
          content: post.content,
          author: post.author,
          likes: post.likes,
          shares: post.shares,
          comments: post.comments,
          engagement: post.engagement,
          publishedAt: new Date(post.publishedAt),
        },
        create: {
          keywordId,
          platform: post.platform.toUpperCase() as any,
          postId: post.id,
          content: post.content,
          author: post.author,
          url: post.url,
          likes: post.likes,
          shares: post.shares,
          comments: post.comments,
          engagement: post.engagement,
          publishedAt: new Date(post.publishedAt),
        },
      });
    }
  }

  private async saveReport(userId: string, keywordId: string, reportData: ReportData) {
    const { insights, trendScore, sentimentScore, newsSummary } = reportData;

    await prisma.report.create({
      data: {
        userId,
        keywordId,
        title: `Daily Report: ${reportData.keyword.term}`,
        summary: newsSummary,
        insights: JSON.stringify(insights),
        recommendations: insights.recommendations.join('\n'),
        trendScore,
        sentimentScore,
        status: 'COMPLETED',
        metadata: {
          newsCount: reportData.newsArticles.length,
          socialPostsCount: reportData.socialPosts.length,
          trendDataPoints: reportData.trendData.length,
        },
      },
    });

    // Update keyword's last report time
    await prisma.keyword.update({
      where: { id: keywordId },
      data: { lastReportAt: new Date() },
    });
  }

  private async sendUserReportEmail(user: any, reports: ReportData[]) {
    try {
      await sendReportEmail(user.email, user.name, reports);
      
      // Mark reports as sent
      const reportIds = await prisma.report.findMany({
        where: {
          userId: user.id,
          reportDate: {
            gte: new Date(new Date().setHours(0, 0, 0, 0)),
          },
        },
        select: { id: true },
      });

      await prisma.report.updateMany({
        where: {
          id: { in: reportIds.map(r => r.id) },
        },
        data: {
          emailSent: true,
          emailSentAt: new Date(),
        },
      });

      logger.info(`Report email sent to ${user.email}`);
    } catch (error) {
      logger.error(`Error sending report email to ${user.email}:`, error);
    }
  }

  async collectHourlyData() {
    logger.info('Starting hourly data collection');

    try {
      // Get all active keywords
      const keywords = await prisma.keyword.findMany({
        where: { isActive: true },
        select: { id: true, term: true },
      });

      for (const keyword of keywords) {
        try {
          // Collect basic trend data
          const trendData = await fetchGoogleTrends(keyword.term, 'now 1-H');
          
          if (trendData.length > 0) {
            const latestTrend = trendData[trendData.length - 1];
            
            await prisma.trendData.create({
              data: {
                keywordId: keyword.id,
                date: new Date(latestTrend.date),
                trendScore: latestTrend.value,
                region: 'US',
                source: 'google_trends',
                rawData: latestTrend,
              },
            });
          }
        } catch (error) {
          logger.error(`Error collecting data for keyword ${keyword.term}:`, error);
        }
      }

      logger.info('Hourly data collection completed');
    } catch (error) {
      logger.error('Error in hourly data collection:', error);
    }
  }

  async cleanupOldData() {
    logger.info('Starting weekly data cleanup');

    try {
      const retentionDays = 90; // Default retention period
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

      // Clean up old trend data
      await prisma.trendData.deleteMany({
        where: {
          createdAt: { lt: cutoffDate },
        },
      });

      // Clean up old news articles
      await prisma.newsArticle.deleteMany({
        where: {
          createdAt: { lt: cutoffDate },
        },
      });

      // Clean up old social posts
      await prisma.socialPost.deleteMany({
        where: {
          createdAt: { lt: cutoffDate },
        },
      });

      // Clean up old reports (keep for longer)
      const reportCutoffDate = new Date();
      reportCutoffDate.setDate(reportCutoffDate.getDate() - (retentionDays * 2));
      
      await prisma.report.deleteMany({
        where: {
          createdAt: { lt: reportCutoffDate },
        },
      });

      logger.info('Weekly data cleanup completed');
    } catch (error) {
      logger.error('Error in weekly data cleanup:', error);
    }
  }
}

// Initialize the report generator
export const reportGenerator = new ReportGenerator();
