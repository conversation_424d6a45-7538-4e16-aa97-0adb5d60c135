{"name": "@react-email/components", "version": "0.0.12", "description": "All react-email components", "sideEffects": false, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist/**"], "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/resendlabs/react-email.git", "directory": "packages/components"}, "keywords": ["react", "email"], "engines": {"node": ">=18.0.0"}, "dependencies": {"@react-email/body": "0.0.4", "@react-email/button": "0.0.11", "@react-email/column": "0.0.8", "@react-email/container": "0.0.10", "@react-email/font": "0.0.4", "@react-email/head": "0.0.6", "@react-email/heading": "0.0.9", "@react-email/hr": "0.0.6", "@react-email/html": "0.0.6", "@react-email/img": "0.0.6", "@react-email/link": "0.0.6", "@react-email/preview": "0.0.7", "@react-email/render": "0.0.9", "@react-email/row": "0.0.6", "@react-email/section": "0.0.10", "@react-email/tailwind": "0.0.13", "@react-email/text": "0.0.6"}, "peerDependencies": {"react": "18.2.0"}, "devDependencies": {"@babel/preset-react": "7.22.5", "typescript": "5.1.6", "eslint-config-custom": "0.0.0", "tsconfig": "0.0.0"}, "publishConfig": {"access": "public"}, "scripts": {"build": "tsup src/index.ts --format esm,cjs --dts --external react", "clean": "rm -rf dist", "dev": "tsup src/index.ts --format esm,cjs --dts --external react --watch", "lint": "eslint ."}}