import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { z } from 'zod';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/db';

const createKeywordSchema = z.object({
  term: z.string().min(1, 'Keyword term is required'),
  description: z.string().optional(),
  category: z.string().optional(),
  tags: z.array(z.string()).optional(),
  priority: z.number().min(1).max(5).optional(),
});

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search');
    const category = searchParams.get('category');
    const isActive = searchParams.get('active');

    const where: any = {
      userId: session.user.id,
    };

    if (search) {
      where.OR = [
        { term: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
      ];
    }

    if (category) {
      where.category = category;
    }

    if (isActive !== null) {
      where.isActive = isActive === 'true';
    }

    const [keywords, total] = await Promise.all([
      prisma.keyword.findMany({
        where,
        include: {
          _count: {
            select: {
              reports: true,
              trendData: true,
              newsArticles: true,
              socialPosts: true,
            },
          },
        },
        orderBy: [
          { priority: 'desc' },
          { createdAt: 'desc' },
        ],
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.keyword.count({ where }),
    ]);

    return NextResponse.json({
      keywords,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching keywords:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { term, description, category, tags, priority } = createKeywordSchema.parse(body);

    // Check if keyword already exists for this user
    const existingKeyword = await prisma.keyword.findUnique({
      where: {
        userId_term: {
          userId: session.user.id,
          term: term.toLowerCase(),
        },
      },
    });

    if (existingKeyword) {
      return NextResponse.json(
        { error: 'Keyword already exists' },
        { status: 400 }
      );
    }

    // Check user's keyword limit
    const userPreferences = await prisma.userPreferences.findUnique({
      where: { userId: session.user.id },
    });

    const keywordCount = await prisma.keyword.count({
      where: { userId: session.user.id, isActive: true },
    });

    const maxKeywords = userPreferences?.maxKeywords || 10;
    if (keywordCount >= maxKeywords) {
      return NextResponse.json(
        { error: `Maximum ${maxKeywords} keywords allowed` },
        { status: 400 }
      );
    }

    const keyword = await prisma.keyword.create({
      data: {
        userId: session.user.id,
        term: term.toLowerCase(),
        description,
        category,
        tags: tags || [],
        priority: priority || 1,
      },
      include: {
        _count: {
          select: {
            reports: true,
            trendData: true,
            newsArticles: true,
            socialPosts: true,
          },
        },
      },
    });

    return NextResponse.json(keyword, { status: 201 });
  } catch (error) {
    console.error('Error creating keyword:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
