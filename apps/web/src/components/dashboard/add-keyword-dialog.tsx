'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  PlusIcon, 
  XMarkIcon,
  MagnifyingGlassIcon,
  SparklesIcon,
} from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { cn } from '@/lib/utils';

const keywordSchema = z.object({
  term: z.string().min(1, 'Keyword is required').max(50, 'Keyword too long'),
  description: z.string().optional(),
  category: z.string().optional(),
  priority: z.number().min(1).max(5).default(1),
});

type KeywordForm = z.infer<typeof keywordSchema>;

const categories = [
  'Technology', 'Business', 'Health', 'Sports', 'Entertainment',
  'Politics', 'Science', 'Education', 'Travel', 'Food'
];

const suggestedKeywords = [
  'artificial intelligence', 'machine learning', 'blockchain',
  'climate change', 'renewable energy', 'electric vehicles',
  'remote work', 'digital marketing', 'cryptocurrency',
  'sustainable fashion', 'mental health', 'space exploration'
];

export function AddKeywordDialog() {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string>('');

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    watch,
  } = useForm<KeywordForm>({
    resolver: zodResolver(keywordSchema),
    defaultValues: {
      priority: 1,
    },
  });

  const watchedTerm = watch('term');

  const onSubmit = async (data: KeywordForm) => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/keywords', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...data,
          category: selectedCategory || undefined,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to create keyword');
      }

      // Reset form and close dialog
      reset();
      setSelectedCategory('');
      setIsOpen(false);
      
      // Refresh the page to show new keyword
      window.location.reload();
    } catch (error) {
      console.error('Error creating keyword:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSuggestedKeyword = (keyword: string) => {
    setValue('term', keyword);
  };

  return (
    <>
      <Button onClick={() => setIsOpen(true)} className="group">
        <PlusIcon className="h-4 w-4 mr-2 transition-transform group-hover:rotate-90" />
        Add Keyword
      </Button>

      <AnimatePresence>
        {isOpen && (
          <>
            {/* Backdrop */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/50 z-50"
              onClick={() => setIsOpen(false)}
            />

            {/* Dialog */}
            <motion.div
              initial={{ opacity: 0, scale: 0.95, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.95, y: 20 }}
              className="fixed left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 z-50 w-full max-w-lg"
            >
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
                  <CardTitle className="flex items-center space-x-2">
                    <SparklesIcon className="h-5 w-5 text-primary" />
                    <span>Add New Keyword</span>
                  </CardTitle>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => setIsOpen(false)}
                  >
                    <XMarkIcon className="h-4 w-4" />
                  </Button>
                </CardHeader>

                <CardContent className="space-y-6">
                  <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                    {/* Keyword Input */}
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Keyword</label>
                      <div className="relative">
                        <MagnifyingGlassIcon className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                        <Input
                          {...register('term')}
                          placeholder="Enter keyword to track..."
                          className="pl-10"
                        />
                      </div>
                      {errors.term && (
                        <p className="text-sm text-red-600">{errors.term.message}</p>
                      )}
                    </div>

                    {/* Description */}
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Description (Optional)</label>
                      <Input
                        {...register('description')}
                        placeholder="What are you tracking this for?"
                      />
                    </div>

                    {/* Category Selection */}
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Category</label>
                      <div className="flex flex-wrap gap-2">
                        {categories.map((category) => (
                          <button
                            key={category}
                            type="button"
                            onClick={() => setSelectedCategory(
                              selectedCategory === category ? '' : category
                            )}
                            className={cn(
                              "px-3 py-1 text-xs rounded-full border transition-colors",
                              selectedCategory === category
                                ? "bg-primary text-primary-foreground border-primary"
                                : "bg-background hover:bg-accent border-border"
                            )}
                          >
                            {category}
                          </button>
                        ))}
                      </div>
                    </div>

                    {/* Priority */}
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Priority</label>
                      <div className="flex space-x-2">
                        {[1, 2, 3, 4, 5].map((priority) => (
                          <button
                            key={priority}
                            type="button"
                            onClick={() => setValue('priority', priority)}
                            className={cn(
                              "w-8 h-8 rounded-full border-2 flex items-center justify-center text-sm font-medium transition-colors",
                              watch('priority') >= priority
                                ? "bg-primary text-primary-foreground border-primary"
                                : "border-border hover:border-primary"
                            )}
                          >
                            {priority}
                          </button>
                        ))}
                      </div>
                      <p className="text-xs text-muted-foreground">
                        Higher priority keywords are processed first
                      </p>
                    </div>

                    {/* Submit Button */}
                    <div className="flex space-x-3 pt-4">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => setIsOpen(false)}
                        className="flex-1"
                      >
                        Cancel
                      </Button>
                      <Button
                        type="submit"
                        loading={isLoading}
                        className="flex-1"
                      >
                        Add Keyword
                      </Button>
                    </div>
                  </form>

                  {/* Suggested Keywords */}
                  {!watchedTerm && (
                    <div className="space-y-3 pt-4 border-t">
                      <h4 className="text-sm font-medium">Suggested Keywords</h4>
                      <div className="flex flex-wrap gap-2">
                        {suggestedKeywords.slice(0, 6).map((keyword) => (
                          <button
                            key={keyword}
                            type="button"
                            onClick={() => handleSuggestedKeyword(keyword)}
                            className="px-3 py-1 text-xs rounded-full bg-muted hover:bg-accent transition-colors"
                          >
                            {keyword}
                          </button>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </>
  );
}
