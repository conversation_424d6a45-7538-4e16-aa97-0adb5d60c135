import axios from 'axios';

const NEWS_API_KEY = process.env.NEWS_API_KEY;
const NEWS_API_BASE_URL = 'https://newsapi.org/v2';

export interface NewsArticle {
  title: string;
  description: string;
  content: string;
  url: string;
  imageUrl?: string;
  source: string;
  author?: string;
  publishedAt: string;
}

export async function fetchNews(keyword: string, days: number = 1): Promise<NewsArticle[]> {
  try {
    const fromDate = new Date();
    fromDate.setDate(fromDate.getDate() - days);
    
    const response = await axios.get(`${NEWS_API_BASE_URL}/everything`, {
      params: {
        q: keyword,
        from: fromDate.toISOString().split('T')[0],
        sortBy: 'relevancy',
        language: 'en',
        pageSize: 20,
        apiKey: NEWS_API_KEY,
      },
    });

    return response.data.articles
      .filter((article: any) => 
        article.title && 
        article.description && 
        !article.title.includes('[Removed]')
      )
      .map((article: any) => ({
        title: article.title,
        description: article.description,
        content: article.content || article.description,
        url: article.url,
        imageUrl: article.urlToImage,
        source: article.source.name,
        author: article.author,
        publishedAt: article.publishedAt,
      }));
  } catch (error) {
    console.error('Error fetching news:', error);
    return [];
  }
}

export async function fetchTopHeadlines(keyword: string): Promise<NewsArticle[]> {
  try {
    const response = await axios.get(`${NEWS_API_BASE_URL}/top-headlines`, {
      params: {
        q: keyword,
        language: 'en',
        pageSize: 10,
        apiKey: NEWS_API_KEY,
      },
    });

    return response.data.articles
      .filter((article: any) => 
        article.title && 
        article.description && 
        !article.title.includes('[Removed]')
      )
      .map((article: any) => ({
        title: article.title,
        description: article.description,
        content: article.content || article.description,
        url: article.url,
        imageUrl: article.urlToImage,
        source: article.source.name,
        author: article.author,
        publishedAt: article.publishedAt,
      }));
  } catch (error) {
    console.error('Error fetching top headlines:', error);
    return [];
  }
}

// Alternative RSS-based news fetching for backup
export async function fetchNewsFromRSS(keyword: string): Promise<NewsArticle[]> {
  try {
    // Google News RSS feed
    const rssUrl = `https://news.google.com/rss/search?q=${encodeURIComponent(keyword)}&hl=en-US&gl=US&ceid=US:en`;
    
    const response = await axios.get('/api/proxy/rss', {
      params: { url: rssUrl },
    });

    // This would be processed by a server-side RSS parser
    return response.data.articles || [];
  } catch (error) {
    console.error('Error fetching RSS news:', error);
    return [];
  }
}

export async function calculateNewsRelevance(article: NewsArticle, keyword: string): Promise<number> {
  const title = article.title.toLowerCase();
  const description = article.description.toLowerCase();
  const keywordLower = keyword.toLowerCase();
  
  let relevance = 0;
  
  // Title contains keyword
  if (title.includes(keywordLower)) {
    relevance += 0.5;
  }
  
  // Description contains keyword
  if (description.includes(keywordLower)) {
    relevance += 0.3;
  }
  
  // Keyword appears multiple times
  const titleMatches = (title.match(new RegExp(keywordLower, 'g')) || []).length;
  const descMatches = (description.match(new RegExp(keywordLower, 'g')) || []).length;
  
  relevance += Math.min((titleMatches + descMatches) * 0.1, 0.2);
  
  return Math.min(relevance, 1.0);
}
