import OpenAI from 'openai';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export interface SentimentAnalysis {
  sentiment: number; // -1 to 1
  confidence: number; // 0 to 1
  emotions: {
    joy: number;
    anger: number;
    fear: number;
    sadness: number;
    surprise: number;
  };
}

export interface TrendInsights {
  summary: string;
  keyPoints: string[];
  recommendations: string[];
  futureOutlook: string;
}

export async function analyzeSentiment(texts: string[]): Promise<SentimentAnalysis[]> {
  try {
    const prompt = `
Analyze the sentiment of the following texts. For each text, provide:
1. Sentiment score (-1 to 1, where -1 is very negative, 0 is neutral, 1 is very positive)
2. Confidence score (0 to 1)
3. Emotion scores (0 to 1 for each: joy, anger, fear, sadness, surprise)

Texts:
${texts.map((text, i) => `${i + 1}. ${text}`).join('\n')}

Respond with a JSON array of objects with the structure:
{
  "sentiment": number,
  "confidence": number,
  "emotions": {
    "joy": number,
    "anger": number,
    "fear": number,
    "sadness": number,
    "surprise": number
  }
}
`;

    const response = await openai.chat.completions.create({
      model: 'gpt-4',
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.3,
    });

    const content = response.choices[0]?.message?.content;
    if (!content) throw new Error('No response from OpenAI');

    return JSON.parse(content);
  } catch (error) {
    console.error('Error analyzing sentiment:', error);
    // Return default neutral sentiment for all texts
    return texts.map(() => ({
      sentiment: 0,
      confidence: 0.5,
      emotions: {
        joy: 0.2,
        anger: 0.2,
        fear: 0.2,
        sadness: 0.2,
        surprise: 0.2,
      },
    }));
  }
}

export async function generateTrendInsights(
  keyword: string,
  newsData: any[],
  socialData: any[],
  trendData: any[]
): Promise<TrendInsights> {
  try {
    const prompt = `
As a trend analysis expert, analyze the following data for the keyword "${keyword}" and provide insights:

News Articles (${newsData.length} articles):
${newsData.slice(0, 5).map(article => `- ${article.title}: ${article.description}`).join('\n')}

Social Media Posts (${socialData.length} posts):
${socialData.slice(0, 5).map(post => `- ${post.content.slice(0, 100)}...`).join('\n')}

Trend Data:
${trendData.map(data => `- Date: ${data.date}, Score: ${data.trendScore}`).join('\n')}

Provide a comprehensive analysis with:
1. A concise summary (2-3 sentences)
2. 3-5 key points about current trends
3. 3-4 actionable recommendations
4. Future outlook (1-2 sentences)

Respond with JSON in this format:
{
  "summary": "string",
  "keyPoints": ["string", "string", ...],
  "recommendations": ["string", "string", ...],
  "futureOutlook": "string"
}
`;

    const response = await openai.chat.completions.create({
      model: 'gpt-4',
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.7,
    });

    const content = response.choices[0]?.message?.content;
    if (!content) throw new Error('No response from OpenAI');

    return JSON.parse(content);
  } catch (error) {
    console.error('Error generating insights:', error);
    return {
      summary: `Current analysis for "${keyword}" shows mixed signals in the market.`,
      keyPoints: [
        'Limited data available for comprehensive analysis',
        'Trend patterns require more observation time',
        'Social sentiment appears neutral'
      ],
      recommendations: [
        'Continue monitoring for emerging patterns',
        'Expand data collection sources',
        'Review analysis in 24-48 hours'
      ],
      futureOutlook: 'Trends may become clearer with additional data collection.'
    };
  }
}

export async function summarizeNews(articles: any[]): Promise<string> {
  try {
    const prompt = `
Summarize the following news articles into a concise 2-3 paragraph summary:

${articles.map((article, i) => `
Article ${i + 1}:
Title: ${article.title}
Description: ${article.description}
`).join('\n')}

Focus on the main themes, important developments, and overall narrative.
`;

    const response = await openai.chat.completions.create({
      model: 'gpt-4',
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.5,
    });

    return response.choices[0]?.message?.content || 'No summary available.';
  } catch (error) {
    console.error('Error summarizing news:', error);
    return 'Unable to generate news summary at this time.';
  }
}

export async function generateWordCloudData(texts: string[]): Promise<Array<{ text: string; value: number }>> {
  try {
    const prompt = `
Extract the most important and relevant keywords from the following texts for a word cloud.
Return 20-30 keywords with their importance scores (1-100).

Texts:
${texts.join('\n')}

Respond with JSON array:
[{"text": "keyword", "value": number}, ...]

Focus on:
- Nouns and important adjectives
- Remove common words (the, and, or, etc.)
- Prioritize domain-specific terms
- Assign higher values to more important/frequent terms
`;

    const response = await openai.chat.completions.create({
      model: 'gpt-4',
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.3,
    });

    const content = response.choices[0]?.message?.content;
    if (!content) throw new Error('No response from OpenAI');

    return JSON.parse(content);
  } catch (error) {
    console.error('Error generating word cloud data:', error);
    return [];
  }
}
