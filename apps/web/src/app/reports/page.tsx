import { Suspense } from 'react';
import { getServerSession } from 'next-auth';
import { redirect } from 'next/navigation';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/db';
import { Navbar } from '@/components/layout/navbar';
import { ReportsHeader } from '@/components/reports/reports-header';
import { ReportsList } from '@/components/reports/reports-list';
import { ReportsFilters } from '@/components/reports/reports-filters';

async function getReportsData(userId: string, searchParams: any) {
  const page = parseInt(searchParams.page || '1');
  const limit = parseInt(searchParams.limit || '10');
  const keyword = searchParams.keyword;
  const status = searchParams.status;
  const dateFrom = searchParams.dateFrom;
  const dateTo = searchParams.dateTo;

  const where: any = { userId };

  if (keyword) {
    where.keyword = {
      term: { contains: keyword, mode: 'insensitive' },
    };
  }

  if (status) {
    where.status = status;
  }

  if (dateFrom || dateTo) {
    where.reportDate = {};
    if (dateFrom) where.reportDate.gte = new Date(dateFrom);
    if (dateTo) where.reportDate.lte = new Date(dateTo);
  }

  const [reports, total, keywords] = await Promise.all([
    prisma.report.findMany({
      where,
      include: {
        keyword: {
          select: { term: true },
        },
      },
      orderBy: { reportDate: 'desc' },
      skip: (page - 1) * limit,
      take: limit,
    }),
    prisma.report.count({ where }),
    prisma.keyword.findMany({
      where: { userId, isActive: true },
      select: { term: true },
      orderBy: { term: 'asc' },
    }),
  ]);

  return {
    reports,
    pagination: {
      page,
      limit,
      total,
      pages: Math.ceil(total / limit),
    },
    keywords: keywords.map(k => k.term),
  };
}

interface ReportsPageProps {
  searchParams: {
    page?: string;
    limit?: string;
    keyword?: string;
    status?: string;
    dateFrom?: string;
    dateTo?: string;
  };
}

export default async function ReportsPage({ searchParams }: ReportsPageProps) {
  const session = await getServerSession(authOptions);

  if (!session?.user?.id) {
    redirect('/auth/signin');
  }

  const { reports, pagination, keywords } = await getReportsData(
    session.user.id,
    searchParams
  );

  return (
    <div className="min-h-screen bg-background">
      <Navbar />
      
      <main className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
        <ReportsHeader totalReports={pagination.total} />
        
        <div className="mt-8 space-y-6">
          <Suspense fallback={<div className="h-16 animate-pulse bg-muted rounded-lg" />}>
            <ReportsFilters 
              keywords={keywords}
              currentFilters={searchParams}
            />
          </Suspense>

          <Suspense fallback={<div className="h-96 animate-pulse bg-muted rounded-lg" />}>
            <ReportsList 
              reports={reports}
              pagination={pagination}
            />
          </Suspense>
        </div>
      </main>
    </div>
  );
}
