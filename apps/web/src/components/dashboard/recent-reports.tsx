'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import { 
  DocumentTextIcon,
  ArrowRightIcon,
  TrendingUpIcon,
  TrendingDownIcon,
} from '@heroicons/react/24/outline';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { formatRelativeTime, cn } from '@/lib/utils';

interface Report {
  id: string;
  title: string;
  reportDate: string;
  trendScore?: number;
  sentimentScore?: number;
  status: string;
  keyword: {
    term: string;
  };
}

interface RecentReportsProps {
  reports: Report[];
}

export function RecentReports({ reports }: RecentReportsProps) {
  if (reports.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <DocumentTextIcon className="h-5 w-5" />
            <span>Recent Reports</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <DocumentTextIcon className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">No reports yet</h3>
            <p className="text-muted-foreground mb-4">
              Reports will appear here once your keywords are analyzed
            </p>
            <Button variant="outline" size="sm">
              Generate Test Report
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
        <CardTitle className="flex items-center space-x-2">
          <DocumentTextIcon className="h-5 w-5" />
          <span>Recent Reports</span>
        </CardTitle>
        <Link href="/reports">
          <Button variant="ghost" size="sm">
            View All
            <ArrowRightIcon className="ml-1 h-4 w-4" />
          </Button>
        </Link>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {reports.map((report, index) => (
          <motion.div
            key={report.id}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <ReportItem report={report} />
          </motion.div>
        ))}
      </CardContent>
    </Card>
  );
}

function ReportItem({ report }: { report: Report }) {
  const trendDirection = (report.trendScore || 0) > 50 ? 'up' : 'down';
  const sentimentColor = 
    (report.sentimentScore || 0) > 0.1 ? 'text-green-600' :
    (report.sentimentScore || 0) < -0.1 ? 'text-red-600' : 
    'text-yellow-600';

  return (
    <Link href={`/reports/${report.id}`}>
      <div className="group flex items-center space-x-4 rounded-lg border p-4 transition-colors hover:bg-accent">
        <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10">
          <DocumentTextIcon className="h-5 w-5 text-primary" />
        </div>
        
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-2 mb-1">
            <h4 className="font-medium capitalize truncate">
              {report.keyword.term}
            </h4>
            <div className="flex items-center space-x-1">
              {trendDirection === 'up' ? (
                <TrendingUpIcon className="h-4 w-4 text-green-600" />
              ) : (
                <TrendingDownIcon className="h-4 w-4 text-red-600" />
              )}
              <span className="text-xs text-muted-foreground">
                {report.trendScore || 0}/100
              </span>
            </div>
          </div>
          
          <div className="flex items-center space-x-4 text-sm text-muted-foreground">
            <span>{formatRelativeTime(report.reportDate)}</span>
            <span className={cn("font-medium", sentimentColor)}>
              {(report.sentimentScore || 0) > 0.1 ? 'Positive' :
               (report.sentimentScore || 0) < -0.1 ? 'Negative' : 'Neutral'}
            </span>
            <span className={cn(
              "px-2 py-1 rounded-full text-xs font-medium",
              report.status === 'COMPLETED' 
                ? "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400"
                : "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400"
            )}>
              {report.status.toLowerCase()}
            </span>
          </div>
        </div>
        
        <ArrowRightIcon className="h-4 w-4 text-muted-foreground transition-transform group-hover:translate-x-1" />
      </div>
    </Link>
  );
}
