{"name": "@react-email/render", "version": "0.0.11", "description": "Transform React components into HTML email templates", "sideEffects": false, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist/**"], "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/resend/react-email.git", "directory": "packages/render"}, "keywords": ["react", "email"], "engines": {"node": ">=18.0.0"}, "dependencies": {"html-to-text": "9.0.5", "js-beautify": "^1.14.11", "react": "18.2.0", "react-dom": "18.2.0"}, "devDependencies": {"@babel/preset-react": "7.22.5", "@edge-runtime/vm": "3.1.7", "@types/html-to-text": "9.0.1", "@types/js-beautify": "1.14.3", "jsdom": "23.0.1", "typescript": "5.1.6", "vitest": "0.34.6", "eslint-config-custom": "0.0.0", "tsconfig": "0.0.0"}, "publishConfig": {"access": "public"}, "scripts": {"build": "tsup src/index.ts --format esm,cjs --dts --external react", "clean": "rm -rf dist", "dev": "tsup src/index.ts --format esm,cjs --dts --external react --watch", "lint": "eslint .", "test:watch": "vitest", "test": "vitest run"}}