'use client';

import { useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { 
  MagnifyingGlassIcon,
  FunnelIcon,
  XMarkIcon,
} from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';

interface ReportsFiltersProps {
  keywords: string[];
  currentFilters: {
    keyword?: string;
    status?: string;
    dateFrom?: string;
    dateTo?: string;
  };
}

const statusOptions = [
  { value: '', label: 'All Status' },
  { value: 'COMPLETED', label: 'Completed' },
  { value: 'PROCESSING', label: 'Processing' },
  { value: 'FAILED', label: 'Failed' },
];

export function ReportsFilters({ keywords, currentFilters }: ReportsFiltersProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isExpanded, setIsExpanded] = useState(false);

  const updateFilters = (newFilters: Record<string, string>) => {
    const params = new URLSearchParams(searchParams);
    
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value) {
        params.set(key, value);
      } else {
        params.delete(key);
      }
    });
    
    // Reset to first page when filters change
    params.delete('page');
    
    router.push(`/reports?${params.toString()}`);
  };

  const clearFilters = () => {
    router.push('/reports');
  };

  const hasActiveFilters = Object.values(currentFilters).some(Boolean);

  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <FunnelIcon className="h-5 w-5 text-muted-foreground" />
            <h3 className="font-medium">Filters</h3>
            {hasActiveFilters && (
              <span className="px-2 py-1 text-xs bg-primary/10 text-primary rounded-full">
                Active
              </span>
            )}
          </div>
          
          <div className="flex items-center space-x-2">
            {hasActiveFilters && (
              <Button
                variant="ghost"
                size="sm"
                onClick={clearFilters}
                className="text-muted-foreground"
              >
                <XMarkIcon className="h-4 w-4 mr-1" />
                Clear
              </Button>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              {isExpanded ? 'Less' : 'More'} Filters
            </Button>
          </div>
        </div>

        <div className="space-y-4">
          {/* Search */}
          <div className="relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search by keyword..."
              value={currentFilters.keyword || ''}
              onChange={(e) => updateFilters({ keyword: e.target.value })}
              className="pl-10"
            />
          </div>

          {/* Expanded Filters */}
          {isExpanded && (
            <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
              {/* Keyword Select */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Keyword</label>
                <select
                  value={currentFilters.keyword || ''}
                  onChange={(e) => updateFilters({ keyword: e.target.value })}
                  className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                >
                  <option value="">All Keywords</option>
                  {keywords.map((keyword) => (
                    <option key={keyword} value={keyword}>
                      {keyword}
                    </option>
                  ))}
                </select>
              </div>

              {/* Status Select */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Status</label>
                <select
                  value={currentFilters.status || ''}
                  onChange={(e) => updateFilters({ status: e.target.value })}
                  className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                >
                  {statusOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>

              {/* Date From */}
              <div className="space-y-2">
                <label className="text-sm font-medium">From Date</label>
                <Input
                  type="date"
                  value={currentFilters.dateFrom || ''}
                  onChange={(e) => updateFilters({ dateFrom: e.target.value })}
                />
              </div>

              {/* Date To */}
              <div className="space-y-2">
                <label className="text-sm font-medium">To Date</label>
                <Input
                  type="date"
                  value={currentFilters.dateTo || ''}
                  onChange={(e) => updateFilters({ dateTo: e.target.value })}
                />
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
