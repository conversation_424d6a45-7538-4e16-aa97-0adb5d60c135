{"version": 3, "sources": ["../../../src/lib/memory/gc-observer.ts"], "names": ["getGcEvents", "startObservingGc", "stopObservingGc", "LONG_RUNNING_GC_THRESHOLD_MS", "gcEvents", "obs", "PerformanceObserver", "list", "entry", "getEntries", "push", "duration", "warn", "bold", "toFixed", "observe", "entryTypes", "disconnect"], "mappings": ";;;;;;;;;;;;;;;;IAiCgBA,WAAW;eAAXA;;IAbAC,gBAAgB;eAAhBA;;IAIAC,eAAe;eAAfA;;;4BAxBoB;qBACf;4BACA;AAErB,MAAMC,+BAA+B;AAErC,MAAMC,WAA+B,EAAE;AACvC,MAAMC,MAAM,IAAIC,+BAAmB,CAAC,CAACC;IACnC,MAAMC,QAAQD,KAAKE,UAAU,EAAE,CAAC,EAAE;IAClCL,SAASM,IAAI,CAACF;IAEd,IAAIA,MAAMG,QAAQ,GAAGR,8BAA8B;QACjDS,IAAAA,SAAI,EAACC,IAAAA,gBAAI,EAAC,CAAC,0BAA0B,EAAEL,MAAMG,QAAQ,CAACG,OAAO,CAAC,GAAG,EAAE,CAAC;IACtE;AACF;AAMO,SAASb;IACdI,IAAIU,OAAO,CAAC;QAAEC,YAAY;YAAC;SAAK;IAAC;AACnC;AAEO,SAASd;IACdG,IAAIY,UAAU;AAChB;AAOO,SAASjB;IACd,OAAOI;AACT"}