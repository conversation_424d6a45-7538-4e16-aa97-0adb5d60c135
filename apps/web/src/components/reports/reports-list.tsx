'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { 
  DocumentTextIcon,
  EyeIcon,
  TrendingUpIcon,
  TrendingDownIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
} from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { formatDateTime, formatRelativeTime, cn } from '@/lib/utils';

interface Report {
  id: string;
  title: string;
  summary: string;
  reportDate: string;
  trendScore?: number;
  sentimentScore?: number;
  status: string;
  keyword: {
    term: string;
  };
}

interface ReportsListProps {
  reports: Report[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export function ReportsList({ reports, pagination }: ReportsListProps) {
  const router = useRouter();
  const searchParams = useSearchParams();

  const updatePage = (page: number) => {
    const params = new URLSearchParams(searchParams);
    params.set('page', page.toString());
    router.push(`/reports?${params.toString()}`);
  };

  if (reports.length === 0) {
    return (
      <Card>
        <CardContent className="p-12 text-center">
          <DocumentTextIcon className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold mb-2">No reports found</h3>
          <p className="text-muted-foreground mb-6">
            Try adjusting your filters or check back later for new reports.
          </p>
          <Button variant="outline">
            Clear Filters
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Reports Grid */}
      <div className="grid gap-6">
        {reports.map((report, index) => (
          <motion.div
            key={report.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <ReportCard report={report} />
          </motion.div>
        ))}
      </div>

      {/* Pagination */}
      {pagination.pages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
            {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
            {pagination.total} reports
          </div>
          
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => updatePage(pagination.page - 1)}
              disabled={pagination.page <= 1}
            >
              <ChevronLeftIcon className="h-4 w-4 mr-1" />
              Previous
            </Button>
            
            <div className="flex items-center space-x-1">
              {Array.from({ length: Math.min(5, pagination.pages) }, (_, i) => {
                const page = i + 1;
                return (
                  <Button
                    key={page}
                    variant={pagination.page === page ? "default" : "outline"}
                    size="sm"
                    onClick={() => updatePage(page)}
                    className="w-8 h-8 p-0"
                  >
                    {page}
                  </Button>
                );
              })}
            </div>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => updatePage(pagination.page + 1)}
              disabled={pagination.page >= pagination.pages}
            >
              Next
              <ChevronRightIcon className="h-4 w-4 ml-1" />
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}

function ReportCard({ report }: { report: Report }) {
  const trendDirection = (report.trendScore || 0) > 50 ? 'up' : 'down';
  const sentimentColor = 
    (report.sentimentScore || 0) > 0.1 ? 'text-green-600' :
    (report.sentimentScore || 0) < -0.1 ? 'text-red-600' : 
    'text-yellow-600';

  return (
    <Card className="group hover:shadow-lg transition-shadow">
      <CardContent className="p-6">
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-2">
              <h3 className="text-lg font-semibold capitalize">
                {report.keyword.term}
              </h3>
              <div className="flex items-center space-x-1">
                {trendDirection === 'up' ? (
                  <TrendingUpIcon className="h-4 w-4 text-green-600" />
                ) : (
                  <TrendingDownIcon className="h-4 w-4 text-red-600" />
                )}
                <span className="text-sm text-muted-foreground">
                  {report.trendScore || 0}/100
                </span>
              </div>
            </div>
            
            <p className="text-muted-foreground text-sm mb-3 line-clamp-2">
              {report.summary}
            </p>
            
            <div className="flex items-center space-x-4 text-sm">
              <span className="text-muted-foreground">
                {formatDateTime(report.reportDate)}
              </span>
              <span className={cn("font-medium", sentimentColor)}>
                {(report.sentimentScore || 0) > 0.1 ? 'Positive' :
                 (report.sentimentScore || 0) < -0.1 ? 'Negative' : 'Neutral'}
              </span>
              <span className={cn(
                "px-2 py-1 rounded-full text-xs font-medium",
                report.status === 'COMPLETED' 
                  ? "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400"
                  : report.status === 'PROCESSING'
                  ? "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400"
                  : "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400"
              )}>
                {report.status.toLowerCase()}
              </span>
            </div>
          </div>
          
          <Link href={`/reports/${report.id}`}>
            <Button variant="outline" size="sm" className="opacity-0 group-hover:opacity-100 transition-opacity">
              <EyeIcon className="h-4 w-4 mr-1" />
              View
            </Button>
          </Link>
        </div>
      </CardContent>
    </Card>
  );
}
