// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id            String    @id @default(cuid())
  name          String?
  email         String    @unique
  emailVerified DateTime?
  image         String?
  password      String?
  timezone      String    @default("UTC")
  emailEnabled  Boolean   @default(true)
  isActive      Boolean   @default(true)
  role          UserRole  @default(USER)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  accounts    Account[]
  sessions    Session[]
  keywords    Keyword[]
  reports     Report[]
  preferences UserPreferences?
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model UserPreferences {
  id                    String  @id @default(cuid())
  userId                String  @unique
  reportTime            String  @default("10:00") // HH:MM format
  emailNotifications    Boolean @default(true)
  pushNotifications     Boolean @default(false)
  weeklyDigest          Boolean @default(true)
  trendAlerts           Boolean @default(true)
  darkMode              Boolean @default(false)
  language              String  @default("en")
  reportFormat          String  @default("detailed") // detailed, summary, minimal
  maxKeywords           Int     @default(10)
  dataRetentionDays     Int     @default(90)

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model Keyword {
  id          String        @id @default(cuid())
  userId      String
  term        String
  description String?
  isActive    Boolean       @default(true)
  priority    Int           @default(1) // 1-5, higher = more important
  category    String?
  tags        String[]      @default([])
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt
  lastReportAt DateTime?

  user         User           @relation(fields: [userId], references: [id], onDelete: Cascade)
  reports      Report[]
  trendData    TrendData[]
  newsArticles NewsArticle[]
  socialPosts  SocialPost[]

  @@unique([userId, term])
  @@index([userId, isActive])
  @@index([term])
}

model Report {
  id              String       @id @default(cuid())
  userId          String
  keywordId       String
  title           String
  summary         String       @db.Text
  insights        String       @db.Text
  recommendations String       @db.Text
  trendScore      Float?       // 0-100
  sentimentScore  Float?       // -1 to 1
  reportDate      DateTime     @default(now())
  status          ReportStatus @default(PENDING)
  emailSent       Boolean      @default(false)
  emailSentAt     DateTime?
  metadata        Json?        // Additional data like charts, stats
  createdAt       DateTime     @default(now())
  updatedAt       DateTime     @updatedAt

  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  keyword Keyword @relation(fields: [keywordId], references: [id], onDelete: Cascade)

  @@index([userId, reportDate])
  @@index([keywordId, reportDate])
  @@index([status])
}

model TrendData {
  id          String   @id @default(cuid())
  keywordId   String
  date        DateTime
  searchVolume Int?
  trendScore  Float?   // Google Trends score
  region      String   @default("US")
  source      String   @default("google_trends")
  rawData     Json?
  createdAt   DateTime @default(now())

  keyword Keyword @relation(fields: [keywordId], references: [id], onDelete: Cascade)

  @@unique([keywordId, date, region, source])
  @@index([keywordId, date])
}

model NewsArticle {
  id          String   @id @default(cuid())
  keywordId   String
  title       String
  description String?  @db.Text
  content     String?  @db.Text
  url         String
  imageUrl    String?
  source      String
  author      String?
  publishedAt DateTime
  sentiment   Float?   // -1 to 1
  relevance   Float?   // 0-1
  language    String   @default("en")
  createdAt   DateTime @default(now())

  keyword Keyword @relation(fields: [keywordId], references: [id], onDelete: Cascade)

  @@unique([keywordId, url])
  @@index([keywordId, publishedAt])
  @@index([publishedAt])
}

model SocialPost {
  id          String     @id @default(cuid())
  keywordId   String
  platform    Platform
  postId      String     // Platform-specific ID
  content     String     @db.Text
  author      String?
  authorId    String?
  url         String?
  likes       Int        @default(0)
  shares      Int        @default(0)
  comments    Int        @default(0)
  sentiment   Float?     // -1 to 1
  engagement  Float?     // Calculated engagement score
  publishedAt DateTime
  createdAt   DateTime   @default(now())

  keyword Keyword @relation(fields: [keywordId], references: [id], onDelete: Cascade)

  @@unique([keywordId, platform, postId])
  @@index([keywordId, publishedAt])
  @@index([platform, publishedAt])
}

model CronJob {
  id          String    @id @default(cuid())
  name        String    @unique
  schedule    String    // Cron expression
  isActive    Boolean   @default(true)
  lastRun     DateTime?
  nextRun     DateTime?
  runCount    Int       @default(0)
  failCount   Int       @default(0)
  lastError   String?   @db.Text
  metadata    Json?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  @@index([isActive, nextRun])
}

model SystemLog {
  id        String   @id @default(cuid())
  level     LogLevel
  message   String   @db.Text
  context   Json?
  userId    String?
  timestamp DateTime @default(now())

  @@index([level, timestamp])
  @@index([userId, timestamp])
}

// Enums
enum UserRole {
  USER
  ADMIN
  MODERATOR
}

enum ReportStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
  CANCELLED
}

enum Platform {
  TWITTER
  REDDIT
  LINKEDIN
  FACEBOOK
  INSTAGRAM
  YOUTUBE
  TIKTOK
}

enum LogLevel {
  ERROR
  WARN
  INFO
  DEBUG
}
