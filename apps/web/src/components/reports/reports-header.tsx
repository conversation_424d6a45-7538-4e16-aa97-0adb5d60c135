'use client';

import { motion } from 'framer-motion';
import { 
  DocumentTextIcon,
  ChartBarIcon,
  CalendarIcon,
} from '@heroicons/react/24/outline';

interface ReportsHeaderProps {
  totalReports: number;
}

export function ReportsHeader({ totalReports }: ReportsHeaderProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-primary/10 via-secondary/10 to-primary/10 p-8"
    >
      {/* Background decoration */}
      <div className="absolute inset-0 bg-grid-white/10 [mask-image:linear-gradient(0deg,white,rgba(255,255,255,0.6))]" />
      
      <div className="relative">
        <div className="flex items-center space-x-3 mb-4">
          <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-primary/20">
            <DocumentTextIcon className="h-6 w-6 text-primary" />
          </div>
          <div>
            <h1 className="text-3xl font-bold">
              Reports Dashboard 📊
            </h1>
            <p className="text-muted-foreground">
              View and analyze your trend reports
            </p>
          </div>
        </div>

        <div className="grid gap-4 sm:grid-cols-3">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.1 }}
            className="flex items-center space-x-3 rounded-lg bg-background/50 p-4 backdrop-blur-sm"
          >
            <DocumentTextIcon className="h-8 w-8 text-primary" />
            <div>
              <p className="text-sm font-medium text-muted-foreground">Total Reports</p>
              <p className="text-lg font-semibold">{totalReports.toLocaleString()}</p>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.2 }}
            className="flex items-center space-x-3 rounded-lg bg-background/50 p-4 backdrop-blur-sm"
          >
            <ChartBarIcon className="h-8 w-8 text-secondary" />
            <div>
              <p className="text-sm font-medium text-muted-foreground">This Month</p>
              <p className="text-lg font-semibold">{Math.floor(totalReports * 0.3)}</p>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.3 }}
            className="flex items-center space-x-3 rounded-lg bg-background/50 p-4 backdrop-blur-sm"
          >
            <CalendarIcon className="h-8 w-8 text-green-600" />
            <div>
              <p className="text-sm font-medium text-muted-foreground">This Week</p>
              <p className="text-lg font-semibold">{Math.floor(totalReports * 0.1)}</p>
            </div>
          </motion.div>
        </div>
      </div>
    </motion.div>
  );
}
