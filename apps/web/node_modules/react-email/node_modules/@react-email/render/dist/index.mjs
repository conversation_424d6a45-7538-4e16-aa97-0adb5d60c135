var __defProp = Object.defineProperty;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __knownSymbol = (name, symbol) => {
  if (symbol = Symbol[name])
    return symbol;
  throw Error("Symbol." + name + " is not defined");
};
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
var __forAwait = (obj, it, method) => (it = obj[__knownSymbol("asyncIterator")]) ? it.call(obj) : (obj = obj[__knownSymbol("iterator")](), it = {}, method = (key, fn) => (fn = obj[key]) && (it[key] = (arg) => new Promise((yes, no, done) => (arg = fn.call(obj, arg), done = arg.done, Promise.resolve(arg.value).then((value) => yes({ value, done }), no)))), method("next"), method("return"), it);

// src/render.ts
import * as ReactDomServer from "react-dom/server";
import { convert } from "html-to-text";

// src/utils/pretty.ts
import { html } from "js-beautify";
var defaults = {
  unformatted: ["code", "pre", "em", "strong", "span"],
  indent_inner_html: true,
  indent_char: " ",
  indent_size: 2,
  sep: "\n"
};
var pretty = (str, options = {}) => {
  return html(str, __spreadValues(__spreadValues({}, defaults), options));
};

// src/plain-text-selectors.ts
var plainTextSelectors = [
  { selector: "img", format: "skip" },
  { selector: "#__react-email-preview", format: "skip" },
  {
    selector: "a",
    options: { linkBrackets: false }
  }
];

// src/render.ts
var render = (component, options) => {
  if (options == null ? void 0 : options.plainText) {
    return renderAsPlainText(component, options);
  }
  const doctype = '<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">';
  const markup = ReactDomServer.renderToStaticMarkup(component);
  const document = `${doctype}${markup}`;
  if (options && options.pretty) {
    return pretty(document);
  }
  return document;
};
var renderAsPlainText = (component, options) => {
  return convert(ReactDomServer.renderToStaticMarkup(component), __spreadValues({
    selectors: plainTextSelectors
  }, (options == null ? void 0 : options.plainText) === true ? options.htmlToTextOptions : {}));
};

// src/render-async.ts
import { convert as convert2 } from "html-to-text";
var decoder = new TextDecoder("utf-8");
var readStream = (readableStream) => __async(void 0, null, function* () {
  let result = "";
  if ("allReady" in readableStream) {
    const reader = readableStream.getReader();
    while (true) {
      const { value, done } = yield reader.read();
      if (done) {
        break;
      }
      result += decoder.decode(value);
    }
  } else {
    try {
      for (var iter = __forAwait(readableStream), more, temp, error; more = !(temp = yield iter.next()).done; more = false) {
        const chunk = temp.value;
        result += decoder.decode(Buffer.from(chunk));
      }
    } catch (temp) {
      error = [temp];
    } finally {
      try {
        more && (temp = iter.return) && (yield temp.call(iter));
      } finally {
        if (error)
          throw error[0];
      }
    }
  }
  return result;
});
var renderAsync = (component, options) => __async(void 0, null, function* () {
  var _a;
  const reactDOMServer = (yield import("react-dom/server")).default;
  const renderToStream = (_a = reactDOMServer.renderToReadableStream) != null ? _a : reactDOMServer.renderToStaticNodeStream;
  const doctype = '<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">';
  const htmlOrReadableStream = yield renderToStream(component);
  const html2 = typeof htmlOrReadableStream === "string" ? htmlOrReadableStream : yield readStream(htmlOrReadableStream);
  if (options == null ? void 0 : options.plainText) {
    return convert2(html2, __spreadValues({
      selectors: plainTextSelectors
    }, options.htmlToTextOptions));
  }
  const document = `${doctype}${html2}`;
  if (options == null ? void 0 : options.pretty) {
    return pretty(document);
  }
  return document;
});
export {
  plainTextSelectors,
  render,
  renderAsync
};
