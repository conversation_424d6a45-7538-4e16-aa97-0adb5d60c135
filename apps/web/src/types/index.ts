import { User, Keyword, Report, TrendData, NewsArticle, SocialPost } from '@prisma/client';

// Extended types with relations
export interface KeywordWithCounts extends Keyword {
  _count: {
    reports: number;
    trendData: number;
    newsArticles: number;
    socialPosts: number;
  };
}

export interface ReportWithKeyword extends Report {
  keyword: {
    term: string;
  };
}

export interface UserWithPreferences extends User {
  preferences?: {
    reportTime: string;
    emailNotifications: boolean;
    darkMode: boolean;
    language: string;
    maxKeywords: number;
  };
}

// API Response types
export interface ApiResponse<T = any> {
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

// Trend Analysis types
export interface TrendAnalysis {
  keyword: string;
  trendScore: number;
  sentimentScore: number;
  insights: {
    summary: string;
    keyPoints: string[];
    recommendations: string[];
    futureOutlook: string;
  };
  data: {
    trends: TrendDataPoint[];
    news: NewsItem[];
    social: SocialItem[];
  };
}

export interface TrendDataPoint {
  date: string;
  value: number;
  formattedDate: string;
  formattedValue: string;
}

export interface NewsItem {
  title: string;
  description: string;
  url: string;
  source: string;
  publishedAt: string;
  relevance?: number;
}

export interface SocialItem {
  id: string;
  platform: 'twitter' | 'reddit';
  content: string;
  author: string;
  url?: string;
  engagement: number;
  sentiment?: number;
  publishedAt: string;
}

// Chart data types
export interface ChartDataPoint {
  date: string;
  value: number;
  label?: string;
}

export interface WordCloudData {
  text: string;
  value: number;
}

// Form types
export interface KeywordFormData {
  term: string;
  description?: string;
  category?: string;
  priority: number;
  tags?: string[];
}

export interface UserPreferencesFormData {
  reportTime: string;
  emailNotifications: boolean;
  pushNotifications: boolean;
  weeklyDigest: boolean;
  trendAlerts: boolean;
  darkMode: boolean;
  language: string;
  timezone: string;
}

// Notification types
export interface Notification {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
  actionUrl?: string;
  actionText?: string;
}

// Dashboard types
export interface DashboardStats {
  totalKeywords: number;
  totalReports: number;
  avgTrendScore: number;
  nextReportTime: string;
}

// Email template types
export interface EmailTemplateData {
  name: string;
  reports: Array<{
    keyword: { term: string };
    trendScore: number;
    sentimentScore: number;
    insights: {
      summary: string;
      keyPoints: string[];
      recommendations: string[];
    };
    newsArticles: Array<{ title: string; url: string }>;
  }>;
  date: string;
}

// Error types
export interface AppError {
  code: string;
  message: string;
  details?: any;
}

// Search and filter types
export interface SearchFilters {
  search?: string;
  category?: string;
  active?: boolean;
  dateFrom?: string;
  dateTo?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// Export utility types
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

// Theme types
export type Theme = 'light' | 'dark' | 'system';

// Status types
export type Status = 'idle' | 'loading' | 'success' | 'error';

// Platform types
export type Platform = 'twitter' | 'reddit' | 'linkedin' | 'facebook' | 'instagram' | 'youtube' | 'tiktok';

// Sentiment types
export type SentimentType = 'positive' | 'negative' | 'neutral';

// Priority levels
export type Priority = 1 | 2 | 3 | 4 | 5;
