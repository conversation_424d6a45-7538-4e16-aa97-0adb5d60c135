import Link from 'next/link';
import { getServerSession } from 'next-auth';
import { redirect } from 'next/navigation';
import {
  ChartBarIcon,
  ClockIcon,
  BellIcon,
  SparklesIcon,
  ArrowRightIcon,
} from '@heroicons/react/24/outline';
import { authOptions } from '@/lib/auth';

const features = [
  {
    name: 'AI-Powered Analysis',
    description: 'Advanced AI algorithms analyze trends, sentiment, and provide actionable insights.',
    icon: SparklesIcon,
  },
  {
    name: 'Daily Reports',
    description: 'Automated daily reports delivered to your inbox at your preferred time.',
    icon: ClockIcon,
  },
  {
    name: 'Real-time Monitoring',
    description: 'Track keywords across news, social media, and search trends in real-time.',
    icon: ChartBarIcon,
  },
  {
    name: 'Smart Notifications',
    description: 'Get notified when significant changes or trending topics emerge.',
    icon: BellIcon,
  },
];

const stats = [
  { name: 'Keywords Tracked', value: '10K+' },
  { name: 'Reports Generated', value: '50K+' },
  { name: 'Active Users', value: '1K+' },
  { name: 'Accuracy Rate', value: '95%' },
];

export default async function HomePage() {
  const session = await getServerSession(authOptions);

  if (session) {
    redirect('/dashboard');
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Navigation */}
      <nav className="border-b">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="flex h-16 items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="h-8 w-8 rounded-lg bg-gradient-to-br from-blue-600 to-purple-600 flex items-center justify-center">
                <ChartBarIcon className="h-5 w-5 text-white" />
              </div>
              <span className="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                TrendPulse
              </span>
            </div>
            <div className="flex items-center space-x-4">
              <Link href="/auth/signin">
                <button className="px-4 py-2 text-sm font-medium text-gray-700 hover:text-gray-900">
                  Sign In
                </button>
              </Link>
              <Link href="/auth/signup">
                <button className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700">
                  Get Started
                </button>
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative overflow-hidden py-20 sm:py-32">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="mx-auto max-w-2xl text-center">
            <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl">
              AI-Powered{' '}
              <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Trend Analysis
              </span>
            </h1>
            <p className="mt-6 text-lg leading-8 text-gray-600">
              Stay ahead of the curve with daily AI-generated trend reports. Track keywords,
              analyze sentiment, and get actionable insights delivered to your inbox.
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Link href="/auth/signup">
                <button className="flex items-center px-6 py-3 text-base font-semibold text-white bg-blue-600 rounded-md hover:bg-blue-700">
                  Start Free Trial
                  <ArrowRightIcon className="ml-2 h-4 w-4" />
                </button>
              </Link>
              <Link href="#features">
                <button className="px-6 py-3 text-base font-semibold text-gray-900 border border-gray-300 rounded-md hover:bg-gray-50">
                  Learn More
                </button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 sm:py-20 bg-gray-50">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="mx-auto max-w-2xl lg:max-w-none">
            <div className="text-center">
              <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                Trusted by thousands of professionals
              </h2>
              <p className="mt-4 text-lg text-gray-600">
                Join the growing community of trend analysts and decision makers.
              </p>
            </div>
            <dl className="mt-16 grid grid-cols-1 gap-0.5 overflow-hidden rounded-2xl text-center sm:grid-cols-2 lg:grid-cols-4">
              {stats.map((stat) => (
                <div key={stat.name} className="flex flex-col bg-white p-8">
                  <dt className="text-sm font-semibold leading-6 text-gray-600">
                    {stat.name}
                  </dt>
                  <dd className="order-first text-3xl font-bold tracking-tight text-gray-900">
                    {stat.value}
                  </dd>
                </div>
              ))}
            </dl>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-16 sm:py-20">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
              Everything you need to track trends
            </h2>
            <p className="mt-6 text-lg leading-8 text-gray-600">
              Comprehensive trend analysis tools powered by cutting-edge AI technology.
            </p>
          </div>
          <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
            <dl className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-2">
              {features.map((feature) => (
                <div key={feature.name} className="relative bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
                  <div className="flex items-center space-x-3 mb-4">
                    <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-blue-600">
                      <feature.icon className="h-6 w-6 text-white" />
                    </div>
                    <h3 className="text-xl font-semibold text-gray-900">{feature.name}</h3>
                  </div>
                  <p className="text-base text-gray-600">
                    {feature.description}
                  </p>
                </div>
              ))}
            </dl>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 sm:py-20 bg-blue-600">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">
              Ready to get started?
            </h2>
            <p className="mt-6 text-lg leading-8 text-blue-100">
              Join thousands of professionals who rely on TrendPulse for their daily trend analysis.
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Link href="/auth/signup">
                <button className="px-6 py-3 text-base font-semibold text-blue-600 bg-white rounded-md hover:bg-gray-50">
                  Start Your Free Trial
                </button>
              </Link>
              <Link href="/auth/signin">
                <button className="px-6 py-3 text-base font-semibold text-white border border-white rounded-md hover:bg-blue-700">
                  Sign In
                </button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="border-t bg-white">
        <div className="mx-auto max-w-7xl px-4 py-12 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="h-6 w-6 rounded bg-gradient-to-br from-blue-600 to-purple-600 flex items-center justify-center">
                <ChartBarIcon className="h-4 w-4 text-white" />
              </div>
              <span className="font-semibold text-gray-900">TrendPulse</span>
            </div>
            <p className="text-sm text-gray-500">
              © 2024 TrendPulse. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
