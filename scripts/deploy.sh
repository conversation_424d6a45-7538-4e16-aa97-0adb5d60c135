#!/bin/bash

# TrendPulse Deployment Script
# This script handles deployment to various environments

set -e

# Configuration
APP_NAME="trendpulse"
DOCKER_REGISTRY="your-registry.com"
DOCKER_IMAGE="$DOCKER_REGISTRY/$APP_NAME"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if environment is provided
if [ -z "$1" ]; then
    log_error "Environment not specified. Usage: ./deploy.sh [development|staging|production]"
    exit 1
fi

ENVIRONMENT=$1
log_info "Deploying to $ENVIRONMENT environment..."

# Validate environment
case $ENVIRONMENT in
    development|staging|production)
        ;;
    *)
        log_error "Invalid environment. Use: development, staging, or production"
        exit 1
        ;;
esac

# Pre-deployment checks
log_info "Running pre-deployment checks..."

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    log_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if required files exist
if [ ! -f "Dockerfile" ]; then
    log_error "Dockerfile not found in current directory"
    exit 1
fi

if [ ! -f "docker-compose.yml" ]; then
    log_error "docker-compose.yml not found in current directory"
    exit 1
fi

# Run tests before deployment
log_info "Running tests..."
if [ "$ENVIRONMENT" != "development" ]; then
    pnpm test || {
        log_error "Tests failed. Deployment aborted."
        exit 1
    }
    log_info "All tests passed ✅"
fi

# Build Docker image
log_info "Building Docker image..."
TIMESTAMP=$(date +%Y%m%d-%H%M%S)
GIT_COMMIT=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
IMAGE_TAG="$ENVIRONMENT-$TIMESTAMP-$GIT_COMMIT"

docker build -t $APP_NAME:$IMAGE_TAG -t $APP_NAME:$ENVIRONMENT-latest . || {
    log_error "Docker build failed"
    exit 1
}

log_info "Docker image built successfully: $APP_NAME:$IMAGE_TAG"

# Environment-specific deployment
case $ENVIRONMENT in
    development)
        log_info "Deploying to development environment..."
        
        # Stop existing containers
        docker-compose down || true
        
        # Start services
        docker-compose up -d
        
        # Wait for services to be ready
        log_info "Waiting for services to be ready..."
        sleep 30
        
        # Run database migrations
        log_info "Running database migrations..."
        docker-compose exec app pnpm --filter web db:migrate || {
            log_warn "Database migration failed, but continuing..."
        }
        
        log_info "Development deployment completed!"
        log_info "Application available at: http://localhost:3000"
        ;;
        
    staging)
        log_info "Deploying to staging environment..."
        
        # Tag and push image to registry
        docker tag $APP_NAME:$IMAGE_TAG $DOCKER_IMAGE:$IMAGE_TAG
        docker tag $APP_NAME:$IMAGE_TAG $DOCKER_IMAGE:staging-latest
        
        log_info "Pushing image to registry..."
        docker push $DOCKER_IMAGE:$IMAGE_TAG
        docker push $DOCKER_IMAGE:staging-latest
        
        # Deploy to staging server (customize based on your infrastructure)
        log_info "Deploying to staging server..."
        # ssh staging-server "docker pull $DOCKER_IMAGE:staging-latest && docker-compose up -d"
        
        log_info "Staging deployment completed!"
        ;;
        
    production)
        log_info "Deploying to production environment..."
        
        # Additional production checks
        log_warn "Deploying to PRODUCTION. This action cannot be undone."
        read -p "Are you sure you want to continue? (yes/no): " confirm
        
        if [ "$confirm" != "yes" ]; then
            log_info "Production deployment cancelled."
            exit 0
        fi
        
        # Tag and push image to registry
        docker tag $APP_NAME:$IMAGE_TAG $DOCKER_IMAGE:$IMAGE_TAG
        docker tag $APP_NAME:$IMAGE_TAG $DOCKER_IMAGE:production-latest
        
        log_info "Pushing image to registry..."
        docker push $DOCKER_IMAGE:$IMAGE_TAG
        docker push $DOCKER_IMAGE:production-latest
        
        # Deploy to production (customize based on your infrastructure)
        log_info "Deploying to production servers..."
        # kubectl set image deployment/trendpulse app=$DOCKER_IMAGE:$IMAGE_TAG
        # or
        # ssh production-server "docker pull $DOCKER_IMAGE:production-latest && docker-compose up -d"
        
        log_info "Production deployment completed!"
        ;;
esac

# Post-deployment checks
log_info "Running post-deployment checks..."

case $ENVIRONMENT in
    development)
        # Check if application is responding
        sleep 10
        if curl -f http://localhost:3000/api/health > /dev/null 2>&1; then
            log_info "Health check passed ✅"
        else
            log_warn "Health check failed ❌"
        fi
        ;;
    staging|production)
        # Add your staging/production health checks here
        log_info "Health checks for $ENVIRONMENT should be implemented"
        ;;
esac

# Cleanup old images (keep last 5)
log_info "Cleaning up old Docker images..."
docker images $APP_NAME --format "table {{.Repository}}\t{{.Tag}}\t{{.CreatedAt}}" | \
    grep -E "$ENVIRONMENT-[0-9]" | \
    tail -n +6 | \
    awk '{print $2}' | \
    xargs -r docker rmi $APP_NAME: 2>/dev/null || true

log_info "Deployment completed successfully! 🚀"

# Display useful information
echo ""
echo "=== Deployment Summary ==="
echo "Environment: $ENVIRONMENT"
echo "Image Tag: $IMAGE_TAG"
echo "Git Commit: $GIT_COMMIT"
echo "Timestamp: $TIMESTAMP"
echo ""

case $ENVIRONMENT in
    development)
        echo "Application URL: http://localhost:3000"
        echo "Database URL: postgresql://postgres:postgres@localhost:5432/trendpulse"
        echo ""
        echo "Useful commands:"
        echo "  View logs: docker-compose logs -f"
        echo "  Stop services: docker-compose down"
        echo "  Database shell: docker-compose exec postgres psql -U postgres -d trendpulse"
        ;;
    staging)
        echo "Staging URL: https://staging.trendpulse.com"
        ;;
    production)
        echo "Production URL: https://trendpulse.com"
        ;;
esac
