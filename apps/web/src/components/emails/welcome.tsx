import {
  Html,
  Head,
  Body,
  Container,
  Section,
  Text,
  Heading,
  Button,
  Hr,
} from '@react-email/components';

interface WelcomeEmailProps {
  name: string;
}

export function WelcomeEmail({ name }: WelcomeEmailProps) {
  const baseUrl = process.env.APP_URL || 'http://localhost:3000';

  return (
    <Html>
      <Head />
      <Body style={main}>
        <Container style={container}>
          {/* Header */}
          <Section style={header}>
            <Heading style={headerTitle}>Welcome to TrendPulse! 🚀</Heading>
          </Section>

          {/* Content */}
          <Section style={content}>
            <Text style={greeting}>Hi {name},</Text>
            
            <Text style={paragraph}>
              Welcome to TrendPulse! We're excited to help you stay ahead of the trends with AI-powered analysis and daily insights.
            </Text>

            <Text style={paragraph}>
              Here's what you can do with your new account:
            </Text>

            <ul style={list}>
              <li style={listItem}>
                <strong>Track Keywords:</strong> Add up to 10 keywords to monitor trends, news, and social sentiment
              </li>
              <li style={listItem}>
                <strong>Daily Reports:</strong> Get comprehensive AI-generated reports delivered to your inbox every morning
              </li>
              <li style={listItem}>
                <strong>Real-time Insights:</strong> Access live trend data, sentiment analysis, and predictive insights
              </li>
              <li style={listItem}>
                <strong>Custom Alerts:</strong> Set up notifications for significant trend changes or viral content
              </li>
            </ul>

            <Text style={paragraph}>
              Ready to get started? Click the button below to set up your first keyword and start tracking trends!
            </Text>

            <Section style={buttonContainer}>
              <Button style={button} href={`${baseUrl}/dashboard`}>
                Get Started
              </Button>
            </Section>

            <Hr style={hr} />

            <Text style={paragraph}>
              <strong>Quick Tips:</strong>
            </Text>

            <ul style={list}>
              <li style={listItem}>
                Start with 2-3 keywords related to your industry or interests
              </li>
              <li style={listItem}>
                Check your email preferences in Settings to customize report timing
              </li>
              <li style={listItem}>
                Use specific terms rather than broad categories for better insights
              </li>
            </ul>

            <Text style={paragraph}>
              If you have any questions, feel free to reach out to our support team. We're here to help you make the most of TrendPulse!
            </Text>

            <Text style={signature}>
              Best regards,<br />
              The TrendPulse Team
            </Text>
          </Section>

          {/* Footer */}
          <Section style={footer}>
            <Text style={footerText}>
              You're receiving this email because you just signed up for TrendPulse.
            </Text>
            <Text style={footerText}>
              <a href={`${baseUrl}/settings`} style={footerLink}>Manage preferences</a> | 
              <a href={`${baseUrl}/support`} style={footerLink}>Get help</a>
            </Text>
          </Section>
        </Container>
      </Body>
    </Html>
  );
}

// Styles
const main = {
  backgroundColor: '#f6f9fc',
  fontFamily: '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
};

const container = {
  backgroundColor: '#ffffff',
  margin: '0 auto',
  padding: '20px 0 48px',
  marginBottom: '64px',
  maxWidth: '600px',
  borderRadius: '10px',
  boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
};

const header = {
  background: 'linear-gradient(135deg, #4F46E5 0%, #22D3EE 100%)',
  padding: '40px 30px',
  textAlign: 'center' as const,
  borderRadius: '10px 10px 0 0',
};

const headerTitle = {
  color: '#ffffff',
  fontSize: '28px',
  fontWeight: 'bold',
  margin: '0',
};

const content = {
  padding: '40px 30px',
};

const greeting = {
  fontSize: '18px',
  fontWeight: 'bold',
  color: '#1f2937',
  margin: '0 0 20px 0',
};

const paragraph = {
  fontSize: '16px',
  color: '#374151',
  lineHeight: '24px',
  margin: '0 0 20px 0',
};

const list = {
  margin: '0 0 20px 0',
  paddingLeft: '20px',
};

const listItem = {
  fontSize: '16px',
  color: '#374151',
  lineHeight: '24px',
  marginBottom: '12px',
};

const buttonContainer = {
  textAlign: 'center' as const,
  margin: '32px 0',
};

const button = {
  backgroundColor: '#4F46E5',
  borderRadius: '8px',
  color: '#ffffff',
  fontSize: '16px',
  fontWeight: 'bold',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'inline-block',
  padding: '16px 32px',
  boxShadow: '0 2px 4px rgba(79, 70, 229, 0.2)',
};

const hr = {
  borderColor: '#e5e7eb',
  margin: '32px 0',
};

const signature = {
  fontSize: '16px',
  color: '#374151',
  lineHeight: '24px',
  margin: '32px 0 0 0',
};

const footer = {
  padding: '30px',
  textAlign: 'center' as const,
  borderTop: '1px solid #e5e7eb',
  backgroundColor: '#f9fafb',
  borderRadius: '0 0 10px 10px',
};

const footerText = {
  fontSize: '12px',
  color: '#6b7280',
  margin: '0 0 8px 0',
};

const footerLink = {
  color: '#4F46E5',
  textDecoration: 'none',
  margin: '0 8px',
};
