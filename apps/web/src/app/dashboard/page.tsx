import { Suspense } from 'react';
import { getServerSession } from 'next-auth';
import { redirect } from 'next/navigation';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/db';
import { Navbar } from '@/components/layout/navbar';
import { DashboardHeader } from '@/components/dashboard/dashboard-header';
import { KeywordGrid } from '@/components/dashboard/keyword-grid';
import { StatsOverview } from '@/components/dashboard/stats-overview';
import { RecentReports } from '@/components/dashboard/recent-reports';
import { AddKeywordDialog } from '@/components/dashboard/add-keyword-dialog';

async function getDashboardData(userId: string) {
  const [keywords, recentReports, stats] = await Promise.all([
    prisma.keyword.findMany({
      where: { userId, isActive: true },
      include: {
        _count: {
          select: {
            reports: true,
            trendData: true,
            newsArticles: true,
            socialPosts: true,
          },
        },
      },
      orderBy: [
        { priority: 'desc' },
        { createdAt: 'desc' },
      ],
    }),
    prisma.report.findMany({
      where: { userId },
      include: {
        keyword: {
          select: { term: true },
        },
      },
      orderBy: { reportDate: 'desc' },
      take: 5,
    }),
    prisma.keyword.aggregate({
      where: { userId, isActive: true },
      _count: { id: true },
    }),
  ]);

  return {
    keywords,
    recentReports,
    totalKeywords: stats._count.id,
  };
}

export default async function DashboardPage() {
  const session = await getServerSession(authOptions);

  if (!session?.user?.id) {
    redirect('/auth/signin');
  }

  const { keywords, recentReports, totalKeywords } = await getDashboardData(session.user.id);

  return (
    <div className="min-h-screen bg-background">
      <Navbar />
      
      <main className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
        <DashboardHeader />
        
        <div className="mt-8 grid gap-8">
          {/* Stats Overview */}
          <Suspense fallback={<div className="h-32 animate-pulse bg-muted rounded-lg" />}>
            <StatsOverview 
              totalKeywords={totalKeywords}
              totalReports={recentReports.length}
            />
          </Suspense>

          {/* Keywords Grid */}
          <div className="grid gap-8 lg:grid-cols-3">
            <div className="lg:col-span-2">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold">Your Keywords</h2>
                <AddKeywordDialog />
              </div>
              
              <Suspense fallback={<div className="h-64 animate-pulse bg-muted rounded-lg" />}>
                <KeywordGrid keywords={keywords} />
              </Suspense>
            </div>

            {/* Recent Reports */}
            <div>
              <h2 className="text-2xl font-bold mb-6">Recent Reports</h2>
              <Suspense fallback={<div className="h-64 animate-pulse bg-muted rounded-lg" />}>
                <RecentReports reports={recentReports} />
              </Suspense>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
