import axios from 'axios';

const TWITTER_BEARER_TOKEN = process.env.TWITTER_BEARER_TOKEN;
const REDDIT_CLIENT_ID = process.env.REDDIT_CLIENT_ID;
const REDDIT_CLIENT_SECRET = process.env.REDDIT_CLIENT_SECRET;

export interface SocialPost {
  id: string;
  platform: 'twitter' | 'reddit';
  content: string;
  author: string;
  authorId?: string;
  url?: string;
  likes: number;
  shares: number;
  comments: number;
  publishedAt: string;
  engagement?: number;
}

// Twitter/X API integration
export async function fetchTwitterPosts(keyword: string, maxResults: number = 20): Promise<SocialPost[]> {
  try {
    const response = await axios.get('https://api.twitter.com/2/tweets/search/recent', {
      headers: {
        'Authorization': `Bearer ${TWITTER_BEARER_TOKEN}`,
      },
      params: {
        query: `${keyword} -is:retweet lang:en`,
        max_results: maxResults,
        'tweet.fields': 'created_at,author_id,public_metrics,context_annotations',
        'user.fields': 'username,name',
        expansions: 'author_id',
      },
    });

    const tweets = response.data.data || [];
    const users = response.data.includes?.users || [];
    
    return tweets.map((tweet: any) => {
      const author = users.find((user: any) => user.id === tweet.author_id);
      const metrics = tweet.public_metrics || {};
      
      return {
        id: tweet.id,
        platform: 'twitter' as const,
        content: tweet.text,
        author: author?.name || 'Unknown',
        authorId: tweet.author_id,
        url: `https://twitter.com/${author?.username}/status/${tweet.id}`,
        likes: metrics.like_count || 0,
        shares: metrics.retweet_count || 0,
        comments: metrics.reply_count || 0,
        publishedAt: tweet.created_at,
        engagement: calculateEngagement(metrics),
      };
    });
  } catch (error) {
    console.error('Error fetching Twitter posts:', error);
    return [];
  }
}

// Reddit API integration
export async function fetchRedditPosts(keyword: string, limit: number = 20): Promise<SocialPost[]> {
  try {
    // First, get Reddit access token
    const tokenResponse = await axios.post(
      'https://www.reddit.com/api/v1/access_token',
      'grant_type=client_credentials',
      {
        headers: {
          'Authorization': `Basic ${Buffer.from(`${REDDIT_CLIENT_ID}:${REDDIT_CLIENT_SECRET}`).toString('base64')}`,
          'Content-Type': 'application/x-www-form-urlencoded',
          'User-Agent': 'TrendPulse/1.0',
        },
      }
    );

    const accessToken = tokenResponse.data.access_token;

    // Search for posts
    const response = await axios.get('https://oauth.reddit.com/search', {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'User-Agent': 'TrendPulse/1.0',
      },
      params: {
        q: keyword,
        sort: 'relevance',
        t: 'day',
        limit: limit,
        type: 'link,sr',
      },
    });

    const posts = response.data.data?.children || [];
    
    return posts
      .filter((post: any) => post.data.selftext || post.data.title)
      .map((post: any) => {
        const data = post.data;
        return {
          id: data.id,
          platform: 'reddit' as const,
          content: data.selftext || data.title,
          author: data.author,
          url: `https://reddit.com${data.permalink}`,
          likes: data.ups || 0,
          shares: 0, // Reddit doesn't have shares
          comments: data.num_comments || 0,
          publishedAt: new Date(data.created_utc * 1000).toISOString(),
          engagement: calculateRedditEngagement(data),
        };
      });
  } catch (error) {
    console.error('Error fetching Reddit posts:', error);
    return [];
  }
}

export async function fetchAllSocialPosts(keyword: string): Promise<SocialPost[]> {
  const [twitterPosts, redditPosts] = await Promise.all([
    fetchTwitterPosts(keyword),
    fetchRedditPosts(keyword),
  ]);

  return [...twitterPosts, ...redditPosts]
    .sort((a, b) => new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime());
}

function calculateEngagement(metrics: any): number {
  const likes = metrics.like_count || 0;
  const retweets = metrics.retweet_count || 0;
  const replies = metrics.reply_count || 0;
  const quotes = metrics.quote_count || 0;
  
  // Weighted engagement score
  return likes + (retweets * 2) + (replies * 1.5) + (quotes * 1.5);
}

function calculateRedditEngagement(data: any): number {
  const upvotes = data.ups || 0;
  const comments = data.num_comments || 0;
  const awards = data.total_awards_received || 0;
  
  // Reddit engagement calculation
  return upvotes + (comments * 2) + (awards * 5);
}

export function calculateSocialSentiment(posts: SocialPost[]): number {
  if (posts.length === 0) return 0;
  
  // This is a simplified sentiment calculation
  // In production, you'd use the OpenAI API for proper sentiment analysis
  let totalSentiment = 0;
  
  posts.forEach(post => {
    const content = post.content.toLowerCase();
    let sentiment = 0;
    
    // Positive words
    const positiveWords = ['good', 'great', 'excellent', 'amazing', 'love', 'best', 'awesome', 'fantastic'];
    const negativeWords = ['bad', 'terrible', 'awful', 'hate', 'worst', 'horrible', 'disappointing'];
    
    positiveWords.forEach(word => {
      if (content.includes(word)) sentiment += 0.1;
    });
    
    negativeWords.forEach(word => {
      if (content.includes(word)) sentiment -= 0.1;
    });
    
    totalSentiment += sentiment;
  });
  
  return totalSentiment / posts.length;
}

export function getTopEngagementPosts(posts: SocialPost[], limit: number = 5): SocialPost[] {
  return posts
    .sort((a, b) => (b.engagement || 0) - (a.engagement || 0))
    .slice(0, limit);
}
