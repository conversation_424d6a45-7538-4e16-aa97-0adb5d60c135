# TrendPulse 📈

> AI-powered trend analysis and daily report generation platform

TrendPulse is a comprehensive web application that helps users track keywords, analyze trends, and receive AI-generated daily reports with insights from news, social media, and search trends.

## ✨ Features

- **🔍 Keyword Tracking**: Monitor up to 10 keywords with real-time trend analysis
- **🤖 AI-Powered Insights**: GPT-4 powered analysis and recommendations
- **📧 Daily Reports**: Automated email reports delivered at your preferred time
- **📊 Multi-Source Data**: Integrates Google Trends, News API, Twitter/X, and Reddit
- **🎨 Modern UI**: Beautiful, responsive interface with dark/light mode
- **🔐 Secure Authentication**: Email/password and OAuth (Google, GitHub)
- **📱 Mobile Responsive**: Optimized for all device sizes
- **⚡ Real-time Updates**: Live trend monitoring and notifications

## 🛠️ Tech Stack

### Frontend
- **Next.js 14** with App Router and Server Components
- **TypeScript** for type safety
- **Tailwind CSS** for styling
- **Framer Motion** for animations
- **TanStack Query** for data fetching
- **Zustand** for state management

### Backend
- **Next.js API Routes** with Edge Runtime
- **Prisma ORM** with PostgreSQL
- **NextAuth.js** for authentication
- **BullMQ** with Redis for job queues
- **Node-cron** for scheduled tasks

### External APIs
- **OpenAI GPT-4** for AI analysis
- **Google Trends API** for trend data
- **News API** for news articles
- **Twitter/X API** for social media data
- **Reddit API** for community insights

### Infrastructure
- **Docker** for containerization
- **PostgreSQL** for database
- **Redis** for caching and queues
- **Resend** for email delivery

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ 
- pnpm 8+
- Docker & Docker Compose
- Git

### 1. Clone the Repository

```bash
git clone https://github.com/your-username/trendpulse.git
cd trendpulse
```

### 2. Run Setup Script

```bash
chmod +x scripts/setup.sh
./scripts/setup.sh
```

This script will:
- Install dependencies
- Create `.env` file from template
- Start Docker services (PostgreSQL, Redis)
- Setup database schema
- Seed initial data

### 3. Configure Environment Variables

Update `.env` with your API keys:

```env
# Database
DATABASE_URL="postgresql://postgres:postgres@localhost:5432/trendpulse"

# Authentication
NEXTAUTH_SECRET="your-secret-key"
GOOGLE_CLIENT_ID="your-google-client-id"
GITHUB_CLIENT_ID="your-github-client-id"

# AI & APIs
OPENAI_API_KEY="your-openai-api-key"
NEWS_API_KEY="your-news-api-key"
TWITTER_BEARER_TOKEN="your-twitter-token"
REDDIT_CLIENT_ID="your-reddit-client-id"

# Email
RESEND_API_KEY="your-resend-api-key"
FROM_EMAIL="<EMAIL>"
```

### 4. Start Development Server

```bash
pnpm dev
```

Visit [http://localhost:3000](http://localhost:3000) to see the application.

## 📋 API Keys Setup

### Required API Keys

1. **OpenAI API Key**
   - Visit [OpenAI Platform](https://platform.openai.com/api-keys)
   - Create new API key
   - Add to `OPENAI_API_KEY`

2. **News API Key**
   - Visit [NewsAPI.org](https://newsapi.org/register)
   - Register for free account
   - Add to `NEWS_API_KEY`

3. **Twitter/X Bearer Token**
   - Visit [Twitter Developer Portal](https://developer.twitter.com/)
   - Create app and generate Bearer Token
   - Add to `TWITTER_BEARER_TOKEN`

4. **Reddit API Credentials**
   - Visit [Reddit App Preferences](https://www.reddit.com/prefs/apps)
   - Create new app
   - Add Client ID and Secret

5. **Resend API Key**
   - Visit [Resend](https://resend.com/)
   - Create account and generate API key
   - Add to `RESEND_API_KEY`

6. **OAuth Providers** (Optional)
   - **Google**: [Google Cloud Console](https://console.cloud.google.com/)
   - **GitHub**: [GitHub Developer Settings](https://github.com/settings/developers)

## 🐳 Docker Deployment

### Development with Docker

```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

### Production Deployment

```bash
# Build production image
docker build -t trendpulse:latest .

# Run with production compose
docker-compose -f docker-compose.prod.yml up -d
```

## 🧪 Testing

### Unit Tests

```bash
# Run unit tests
pnpm test

# Run with coverage
pnpm test --coverage

# Watch mode
pnpm test --watch
```

### E2E Tests

```bash
# Install Playwright browsers
pnpm --filter web playwright install

# Run E2E tests
pnpm test:e2e

# Run in UI mode
pnpm test:e2e --ui
```

## 📊 Database Management

### Prisma Commands

```bash
# Generate Prisma client
pnpm db:generate

# Push schema changes
pnpm db:push

# Create migration
pnpm db:migrate

# Open Prisma Studio
pnpm db:studio

# Reset database
pnpm db:reset
```

### Database Schema

The application uses PostgreSQL with the following main entities:

- **Users**: User accounts and preferences
- **Keywords**: Tracked keywords with metadata
- **Reports**: Generated daily reports
- **TrendData**: Google Trends data points
- **NewsArticles**: Collected news articles
- **SocialPosts**: Social media posts
- **CronJobs**: Scheduled task management

## 🔧 Development

### Project Structure

```
trendpulse/
├── apps/web/                 # Next.js application
│   ├── src/
│   │   ├── app/             # App Router pages
│   │   ├── components/      # React components
│   │   ├── lib/             # Utilities and APIs
│   │   ├── hooks/           # Custom React hooks
│   │   └── types/           # TypeScript types
│   ├── prisma/              # Database schema
│   └── public/              # Static assets
├── packages/                # Shared packages
├── scripts/                 # Setup and deployment scripts
└── docs/                    # Documentation
```

### Code Quality

```bash
# Lint code
pnpm lint

# Format code
pnpm format

# Type check
pnpm type-check

# Run all checks
pnpm check-all
```

### Git Hooks

The project uses Husky for git hooks:

- **pre-commit**: Runs linting and formatting
- **commit-msg**: Validates commit message format
- **pre-push**: Runs tests before pushing

## 📈 Monitoring & Logging

### Application Logs

Logs are structured using Pino:

```bash
# View application logs
docker-compose logs app

# View worker logs
docker-compose logs worker

# Follow logs in real-time
docker-compose logs -f
```

### Health Checks

- **Application**: `GET /api/health`
- **Database**: `GET /api/health/db`
- **Redis**: `GET /api/health/redis`

## 🚀 Deployment Options

### Vercel (Recommended)

1. Connect GitHub repository to Vercel
2. Configure environment variables
3. Deploy automatically on push to main

### Railway

1. Connect GitHub repository
2. Configure services (app, database, redis)
3. Set environment variables

### Self-hosted

1. Use provided Docker Compose configuration
2. Configure reverse proxy (Nginx)
3. Set up SSL certificates
4. Configure monitoring

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

### Development Guidelines

- Follow TypeScript best practices
- Write tests for new features
- Update documentation
- Follow conventional commit format
- Ensure all checks pass

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: Check the `/docs` folder
- **Issues**: Create GitHub issue
- **Discussions**: Use GitHub Discussions
- **Email**: <EMAIL>

## 🙏 Acknowledgments

- [Next.js](https://nextjs.org/) for the amazing framework
- [Prisma](https://prisma.io/) for database tooling
- [Tailwind CSS](https://tailwindcss.com/) for styling
- [OpenAI](https://openai.com/) for AI capabilities
- All the open-source contributors

---

Made with ❤️ by the TrendPulse Team
