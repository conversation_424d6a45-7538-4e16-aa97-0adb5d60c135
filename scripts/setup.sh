#!/bin/bash

# TrendPulse Setup Script
# This script sets up the development environment for TrendPulse

set -e

echo "🚀 Setting up TrendPulse development environment..."

# Check if required tools are installed
check_tool() {
    if ! command -v $1 &> /dev/null; then
        echo "❌ $1 is not installed. Please install it first."
        exit 1
    fi
}

echo "📋 Checking required tools..."
check_tool "node"
check_tool "pnpm"
check_tool "docker"
check_tool "docker-compose"

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js version 18 or higher is required. Current version: $(node -v)"
    exit 1
fi

echo "✅ All required tools are installed"

# Install dependencies
echo "📦 Installing dependencies..."
pnpm install

# Copy environment file
if [ ! -f ".env" ]; then
    echo "📝 Creating .env file from .env.example..."
    cp .env.example .env
    echo "⚠️  Please update the .env file with your actual API keys and configuration"
else
    echo "✅ .env file already exists"
fi

# Start Docker services
echo "🐳 Starting Docker services..."
docker-compose up -d postgres redis

# Wait for services to be ready
echo "⏳ Waiting for services to be ready..."
sleep 10

# Setup database
echo "🗄️  Setting up database..."
cd apps/web
pnpm db:generate
pnpm db:push

# Seed database (optional)
if [ -f "prisma/seed.ts" ]; then
    echo "🌱 Seeding database..."
    pnpm db:seed
fi

cd ../..

echo "✅ Setup complete!"
echo ""
echo "🎉 TrendPulse is ready for development!"
echo ""
echo "Next steps:"
echo "1. Update your .env file with actual API keys"
echo "2. Run 'pnpm dev' to start the development server"
echo "3. Visit http://localhost:3000 to see your application"
echo ""
echo "Useful commands:"
echo "- pnpm dev          Start development server"
echo "- pnpm build        Build for production"
echo "- pnpm test         Run unit tests"
echo "- pnpm test:e2e     Run E2E tests"
echo "- pnpm db:studio    Open Prisma Studio"
echo "- pnpm lint         Run linter"
echo ""
echo "Docker commands:"
echo "- docker-compose up -d     Start services"
echo "- docker-compose down      Stop services"
echo "- docker-compose logs      View logs"
