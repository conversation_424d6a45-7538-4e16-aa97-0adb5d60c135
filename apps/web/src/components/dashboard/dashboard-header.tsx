'use client';

import { useSession } from 'next-auth/react';
import { motion } from 'framer-motion';
import { 
  ChartBarIcon, 
  SparklesIcon,
  ClockIcon,
} from '@heroicons/react/24/outline';

export function DashboardHeader() {
  const { data: session } = useSession();
  
  const currentHour = new Date().getHours();
  const greeting = 
    currentHour < 12 ? 'Good morning' :
    currentHour < 18 ? 'Good afternoon' : 
    'Good evening';

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-primary/10 via-secondary/10 to-primary/10 p-8"
    >
      {/* Background decoration */}
      <div className="absolute inset-0 bg-grid-white/10 [mask-image:linear-gradient(0deg,white,rgba(255,255,255,0.6))]" />
      
      <div className="relative">
        <div className="flex items-center space-x-3 mb-4">
          <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-primary/20">
            <ChartBarIcon className="h-6 w-6 text-primary" />
          </div>
          <div>
            <h1 className="text-3xl font-bold">
              {greeting}, {session?.user?.name?.split(' ')[0] || 'there'}! 👋
            </h1>
            <p className="text-muted-foreground">
              Here's your trend analysis dashboard
            </p>
          </div>
        </div>

        <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.1 }}
            className="flex items-center space-x-3 rounded-lg bg-background/50 p-4 backdrop-blur-sm"
          >
            <SparklesIcon className="h-8 w-8 text-primary" />
            <div>
              <p className="text-sm font-medium text-muted-foreground">AI Analysis</p>
              <p className="text-lg font-semibold">Powered by GPT-4</p>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.2 }}
            className="flex items-center space-x-3 rounded-lg bg-background/50 p-4 backdrop-blur-sm"
          >
            <ClockIcon className="h-8 w-8 text-secondary" />
            <div>
              <p className="text-sm font-medium text-muted-foreground">Daily Reports</p>
              <p className="text-lg font-semibold">10:00 AM</p>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.3 }}
            className="flex items-center space-x-3 rounded-lg bg-background/50 p-4 backdrop-blur-sm sm:col-span-2 lg:col-span-1"
          >
            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-green-100 dark:bg-green-900">
              <div className="h-2 w-2 rounded-full bg-green-600 animate-pulse" />
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">Status</p>
              <p className="text-lg font-semibold text-green-600">All Systems Active</p>
            </div>
          </motion.div>
        </div>
      </div>
    </motion.div>
  );
}
