'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  TrendingUpIcon, 
  TrendingDownIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  ChartBarIcon,
  NewspaperIcon,
  ChatBubbleLeftRightIcon,
} from '@heroicons/react/24/outline';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { cn, formatRelativeTime, capitalizeFirst } from '@/lib/utils';

interface Keyword {
  id: string;
  term: string;
  description?: string;
  category?: string;
  priority: number;
  isActive: boolean;
  createdAt: string;
  lastReportAt?: string;
  _count: {
    reports: number;
    trendData: number;
    newsArticles: number;
    socialPosts: number;
  };
}

interface KeywordGridProps {
  keywords: Keyword[];
}

export function KeywordGrid({ keywords }: KeywordGridProps) {
  const [selectedKeyword, setSelectedKeyword] = useState<string | null>(null);

  if (keywords.length === 0) {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="text-center py-12"
      >
        <ChartBarIcon className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
        <h3 className="text-lg font-semibold mb-2">No keywords yet</h3>
        <p className="text-muted-foreground mb-6">
          Start tracking trends by adding your first keyword
        </p>
        <Button>Add Your First Keyword</Button>
      </motion.div>
    );
  }

  return (
    <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-1 xl:grid-cols-2">
      <AnimatePresence>
        {keywords.map((keyword, index) => (
          <motion.div
            key={keyword.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ delay: index * 0.1 }}
          >
            <KeywordCard 
              keyword={keyword}
              isSelected={selectedKeyword === keyword.id}
              onSelect={() => setSelectedKeyword(
                selectedKeyword === keyword.id ? null : keyword.id
              )}
            />
          </motion.div>
        ))}
      </AnimatePresence>
    </div>
  );
}

interface KeywordCardProps {
  keyword: Keyword;
  isSelected: boolean;
  onSelect: () => void;
}

function KeywordCard({ keyword, isSelected, onSelect }: KeywordCardProps) {
  const trendDirection = Math.random() > 0.5 ? 'up' : 'down'; // Mock trend direction
  const trendValue = Math.floor(Math.random() * 100); // Mock trend value

  return (
    <Card 
      className={cn(
        "cursor-pointer transition-all duration-200 hover:shadow-lg",
        isSelected && "ring-2 ring-primary"
      )}
      onClick={onSelect}
    >
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg capitalize">{keyword.term}</CardTitle>
            {keyword.description && (
              <p className="text-sm text-muted-foreground mt-1">
                {keyword.description}
              </p>
            )}
          </div>
          
          <div className="flex items-center space-x-2">
            {keyword.category && (
              <Badge variant="secondary" className="text-xs">
                {capitalizeFirst(keyword.category)}
              </Badge>
            )}
            <div className="flex items-center space-x-1">
              {Array.from({ length: keyword.priority }).map((_, i) => (
                <div key={i} className="h-1 w-1 rounded-full bg-primary" />
              ))}
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Trend Indicator */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {trendDirection === 'up' ? (
              <TrendingUpIcon className="h-5 w-5 text-green-600" />
            ) : (
              <TrendingDownIcon className="h-5 w-5 text-red-600" />
            )}
            <span className={cn(
              "text-sm font-medium",
              trendDirection === 'up' ? "text-green-600" : "text-red-600"
            )}>
              {trendDirection === 'up' ? '+' : '-'}{trendValue}%
            </span>
          </div>
          
          <div className="text-xs text-muted-foreground">
            {keyword.lastReportAt 
              ? `Last report ${formatRelativeTime(keyword.lastReportAt)}`
              : 'No reports yet'
            }
          </div>
        </div>

        {/* Data Stats */}
        <div className="grid grid-cols-3 gap-3 text-center">
          <div className="space-y-1">
            <div className="flex items-center justify-center">
              <NewspaperIcon className="h-4 w-4 text-blue-600" />
            </div>
            <div className="text-sm font-medium">{keyword._count.newsArticles}</div>
            <div className="text-xs text-muted-foreground">News</div>
          </div>
          
          <div className="space-y-1">
            <div className="flex items-center justify-center">
              <ChatBubbleLeftRightIcon className="h-4 w-4 text-purple-600" />
            </div>
            <div className="text-sm font-medium">{keyword._count.socialPosts}</div>
            <div className="text-xs text-muted-foreground">Social</div>
          </div>
          
          <div className="space-y-1">
            <div className="flex items-center justify-center">
              <ChartBarIcon className="h-4 w-4 text-green-600" />
            </div>
            <div className="text-sm font-medium">{keyword._count.reports}</div>
            <div className="text-xs text-muted-foreground">Reports</div>
          </div>
        </div>

        {/* Actions */}
        <AnimatePresence>
          {isSelected && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="flex items-center justify-between pt-3 border-t"
            >
              <div className="flex space-x-2">
                <Button size="sm" variant="outline">
                  <EyeIcon className="h-4 w-4 mr-1" />
                  View
                </Button>
                <Button size="sm" variant="outline">
                  <PencilIcon className="h-4 w-4 mr-1" />
                  Edit
                </Button>
              </div>
              
              <Button size="sm" variant="outline" className="text-red-600 hover:text-red-700">
                <TrashIcon className="h-4 w-4" />
              </Button>
            </motion.div>
          )}
        </AnimatePresence>
      </CardContent>
    </Card>
  );
}

function Badge({ children, variant = "default", className }: {
  children: React.ReactNode;
  variant?: "default" | "secondary";
  className?: string;
}) {
  return (
    <span className={cn(
      "inline-flex items-center rounded-full px-2 py-1 text-xs font-medium",
      variant === "default" && "bg-primary text-primary-foreground",
      variant === "secondary" && "bg-secondary text-secondary-foreground",
      className
    )}>
      {children}
    </span>
  );
}
