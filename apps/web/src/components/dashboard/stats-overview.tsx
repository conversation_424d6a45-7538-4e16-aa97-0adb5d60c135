'use client';

import { motion } from 'framer-motion';
import { 
  ChartBarIcon,
  DocumentTextIcon,
  TrendingUpIcon,
  ClockIcon,
} from '@heroicons/react/24/outline';
import { Card, CardContent } from '@/components/ui/card';

interface StatsOverviewProps {
  totalKeywords: number;
  totalReports: number;
}

export function StatsOverview({ totalKeywords, totalReports }: StatsOverviewProps) {
  const stats = [
    {
      name: 'Active Keywords',
      value: totalKeywords,
      icon: ChartBarIcon,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100 dark:bg-blue-900/20',
      change: '+12%',
      changeType: 'positive' as const,
    },
    {
      name: 'Reports Generated',
      value: totalReports,
      icon: DocumentTextIcon,
      color: 'text-green-600',
      bgColor: 'bg-green-100 dark:bg-green-900/20',
      change: '+8%',
      changeType: 'positive' as const,
    },
    {
      name: 'Avg. Trend Score',
      value: 78,
      icon: TrendingUpIcon,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100 dark:bg-purple-900/20',
      change: '+5%',
      changeType: 'positive' as const,
    },
    {
      name: 'Next Report',
      value: '10:00 AM',
      icon: ClockIcon,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100 dark:bg-orange-900/20',
      change: 'Tomorrow',
      changeType: 'neutral' as const,
    },
  ];

  return (
    <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-4">
      {stats.map((stat, index) => (
        <motion.div
          key={stat.name}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: index * 0.1 }}
        >
          <Card className="relative overflow-hidden">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <p className="text-sm font-medium text-muted-foreground">
                    {stat.name}
                  </p>
                  <p className="text-2xl font-bold mt-1">
                    {typeof stat.value === 'number' ? stat.value.toLocaleString() : stat.value}
                  </p>
                  <div className="flex items-center mt-2">
                    <span className={`text-xs font-medium ${
                      stat.changeType === 'positive' 
                        ? 'text-green-600' 
                        : stat.changeType === 'negative'
                        ? 'text-red-600'
                        : 'text-muted-foreground'
                    }`}>
                      {stat.change}
                    </span>
                    <span className="text-xs text-muted-foreground ml-1">
                      vs last week
                    </span>
                  </div>
                </div>
                
                <div className={`flex h-12 w-12 items-center justify-center rounded-lg ${stat.bgColor}`}>
                  <stat.icon className={`h-6 w-6 ${stat.color}`} />
                </div>
              </div>

              {/* Background decoration */}
              <div className="absolute -right-4 -top-4 h-16 w-16 rounded-full bg-gradient-to-br from-primary/10 to-secondary/10 opacity-50" />
            </CardContent>
          </Card>
        </motion.div>
      ))}
    </div>
  );
}
