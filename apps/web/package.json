{"name": "@trendpulse/web", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "vitest", "test:e2e": "playwright test", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "type-check": "tsc --noEmit"}, "dependencies": {"next": "^14.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "@prisma/client": "^5.8.0", "@next-auth/prisma-adapter": "^1.0.7", "next-auth": "^4.24.5", "@tanstack/react-query": "^5.17.0", "zustand": "^4.4.7", "framer-motion": "^10.18.0", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "tailwindcss": "^3.4.0", "autoprefixer": "^10.4.16", "postcss": "^8.4.33", "clsx": "^2.1.0", "tailwind-merge": "^2.2.0", "class-variance-authority": "^0.7.0", "lucide-react": "^0.312.0", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "node-cron": "^3.0.3", "bullmq": "^4.15.0", "ioredis": "^5.3.2", "resend": "^3.0.0", "react-email": "^1.10.0", "@react-email/components": "^0.0.12", "openai": "^4.24.1", "axios": "^1.6.5", "cheerio": "^1.0.0-rc.12", "rss-parser": "^3.13.0", "google-trends-api": "^4.9.2", "recharts": "^2.10.3", "react-wordcloud": "^1.2.7", "pino": "^8.17.2", "pino-pretty": "^10.3.1", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "sharp": "^0.33.1"}, "devDependencies": {"@types/react": "^18.2.48", "@types/react-dom": "^18.2.18", "@types/node": "^20.11.0", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/node-cron": "^3.0.11", "typescript": "^5.3.3", "eslint": "^8.56.0", "eslint-config-next": "^14.1.0", "@typescript-eslint/eslint-plugin": "^6.18.1", "@typescript-eslint/parser": "^6.18.1", "prettier": "^3.2.0", "prettier-plugin-tailwindcss": "^0.5.10", "prisma": "^5.8.0", "vitest": "^1.2.0", "@vitejs/plugin-react": "^4.2.1", "playwright": "^1.40.1", "@playwright/test": "^1.40.1", "husky": "^8.0.3", "lint-staged": "^15.2.0"}}